# Assessment Creator Documentation

## Overview

The Assessment Creator is a comprehensive tool for building custom assessment templates within the admin dashboard. It provides a visual form builder interface with drag-and-drop functionality to create complex, nested assessments that output a specific JSON structure.

## Features

### Core Functionality
- **Visual Form Builder**: Drag-and-drop interface for building assessments
- **Real-time Preview**: Live preview of how assessments will appear to end users
- **Template Management**: Full CRUD operations for assessment templates
- **Organization Scoped**: Templates are linked to specific organizations
- **JSON Structure Validation**: Ensures output matches required format

### Question Types Supported
1. **Single Choice** (`"type": "single"`) - Radio button selection
2. **Multiple Choice** (`"type": "multi"`) - Checkbox selection with nested sub-questions
3. **Text Input** (`"type": "multiline"`) - Long text responses
4. **Nested Questions** - Questions can contain sub-questions with different levels

### Assessment Features
- **Assessment Metadata**: Name, banner image URL, scoring legends
- **Section Management**: Add/remove/reorder sections
- **Question Builder**: Configure question titles, messages, values, and scoring
- **Nested Sub-questions**: Support for complex question hierarchies
- **Scoring Support**: Optional scoring system for assessments
- **Multi-language**: Support for English and Spanish

## Technical Implementation

### Backend Components

#### AssessmentCreatorController
- **Location**: `Sources/App/Controllers/AssessmentCreatorController.swift`
- **Routes**: All routes are protected with `AdminAuthMiddleware()`
- **Base Path**: `/admin/assessment-creator`

**Available Endpoints:**
```
GET    /admin/assessment-creator                    - Dashboard
GET    /admin/assessment-creator/templates          - List templates (API)
GET    /admin/assessment-creator/create             - Create form
POST   /admin/assessment-creator/create             - Create template
GET    /admin/assessment-creator/:id/edit           - Edit form
PUT    /admin/assessment-creator/:id                - Update template
DELETE /admin/assessment-creator/:id                - Delete template
GET    /admin/assessment-creator/:id/preview        - Preview template (API)
POST   /admin/assessment-creator/:id/duplicate      - Duplicate template
```

#### Data Structures
- **AssessmentTemplateInput**: Input validation for template creation/updates
- **AssessmentTemplateData**: Template data for editing
- **AssessmentTemplate**: JSON structure models for validation
- **AssessmentSection/Question**: Nested structure components

### Frontend Components

#### Templates
1. **assessment-creator-dashboard.leaf**: Main dashboard showing template list
2. **assessment-creator-form.leaf**: Visual form builder interface

#### JavaScript Features
- **Drag & Drop**: Sortable.js integration for question reordering
- **Real-time Preview**: Live assessment preview as you build
- **JSON Generation**: Automatic JSON structure generation
- **Form Validation**: Client-side validation before saving
- **AJAX Operations**: Asynchronous save/delete/duplicate operations

## JSON Output Structure

The Assessment Creator generates templates that produce this exact JSON format:

```json
{
  "name": "Assessment Form",
  "banner": "https://res.cloudinary.com/cg1-solutions/image/upload/v1727229877/banners/empowered/empowered_banner_3x_y8flzx.png",
  "legend": [
    {
      "rows": [
        ["0 – 4", "None-minimal", "None"],
        ["5 – 9", "Mild", "Watchful waiting; repeat PHQ-9 at follow-up"]
      ],
      "headers": ["PHQ-9 Score", "Depression Severity", "Proposed Treatment Actions"]
    }
  ],
  "sections": [
    {
      "type": "",
      "title": "Overview",
      "complete": false,
      "questions": [
        {
          "title": "Question Title",
          "message": "Question text displayed to user",
          "level": 1,
          "value": 0,
          "score": 0,
          "type": "single",
          "questions": [
            {
              "title": "Sub-question",
              "message": "Nested question text",
              "level": 2,
              "value": 1,
              "score": 1,
              "type": "multi",
              "questions": []
            }
          ]
        }
      ]
    }
  ]
}
```

## Usage Guide

### Creating an Assessment

1. **Access the Creator**
   - Navigate to Admin Dashboard
   - Click "Assessment Creator"
   - Click "Create New Assessment"

2. **Configure Assessment Settings**
   - Enter assessment name and unique key
   - Set status (draft/active/archived)
   - Choose language (English/Spanish)
   - Enable scoring if needed
   - Add banner image URL (optional)

3. **Build Assessment Structure**
   - Drag "Section" from sidebar to create sections
   - Drag question types into sections
   - Configure each question's properties
   - Add nested sub-questions for complex logic

4. **Preview and Save**
   - Use live preview to see user experience
   - Click "Preview" for full-screen preview
   - Save when satisfied with structure

### Managing Templates

- **Edit**: Click edit button on any template
- **Duplicate**: Create copies of existing templates
- **Delete**: Remove templates (with confirmation)
- **Preview**: View how assessment appears to users

## Integration with Existing System

### Template Model Integration
- Uses existing `Template` model with `kind = "assessment"`
- Stores JSON in `JsonWrapper` field for PostgreSQL JSONB support
- Maintains organization scoping through `orgID` field

### Admin Authentication
- Protected by `AdminAuthMiddleware`
- Requires admin role (`drop_all` or role containing "admin")
- Session-based authentication with 24-hour timeout

### Database Storage
- Templates stored in existing `templates` table
- JSON structure stored as JSONB for efficient querying
- Automatic timestamps for created/updated tracking

## Security Considerations

- **Admin Only Access**: All routes protected by admin middleware
- **Organization Scoping**: Users can only access their organization's templates
- **Input Validation**: JSON structure validation on save
- **XSS Prevention**: Proper escaping in Leaf templates
- **CSRF Protection**: Form-based operations use proper methods

## Future Enhancements

### Planned Features
- **Import/Export**: Template import/export functionality
- **Template Library**: Shared template repository
- **Advanced Scoring**: Complex scoring algorithms
- **Conditional Logic**: Show/hide questions based on responses
- **Template Versioning**: Track template changes over time

### Technical Improvements
- **Drag & Drop Enhancements**: Better visual feedback
- **Undo/Redo**: Action history for builder
- **Auto-save**: Periodic saving of work in progress
- **Collaboration**: Multi-user editing capabilities

## Troubleshooting

### Common Issues

1. **Template Not Saving**
   - Check JSON structure validity
   - Verify admin permissions
   - Ensure required fields are filled

2. **Drag & Drop Not Working**
   - Verify Sortable.js is loaded
   - Check browser console for errors
   - Ensure proper event listeners

3. **Preview Not Updating**
   - Check JavaScript console for errors
   - Verify assessment data structure
   - Refresh page if needed

### Error Messages
- **"Invalid JSON structure"**: Template JSON is malformed
- **"Access denied"**: User lacks admin permissions
- **"Template not found"**: Template ID is invalid or deleted
- **"User organization not found"**: User not properly associated with organization

## API Reference

### Template Creation
```http
POST /admin/assessment-creator/create
Content-Type: application/json

{
  "name": "Assessment Name",
  "key": "assessment_key",
  "status": "draft",
  "language": "en-us",
  "scored": false,
  "templateJson": "{...}"
}
```

### Template Update
```http
PUT /admin/assessment-creator/:id
Content-Type: application/json

{
  "name": "Updated Name",
  "key": "updated_key",
  "status": "active",
  "language": "en-us",
  "scored": true,
  "templateJson": "{...}"
}
```

### Template Deletion
```http
DELETE /admin/assessment-creator/:id
```

Response: `200 OK` on success

This comprehensive Assessment Creator provides a powerful, user-friendly interface for building complex assessments while maintaining the exact JSON structure required by the existing system.
