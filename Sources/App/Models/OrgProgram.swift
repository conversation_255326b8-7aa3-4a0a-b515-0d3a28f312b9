//
//  OrgProgram.swift
//  
//
//  Created by Augment Agent on 9/21/25.
//

import Foundation
import Fluent
import Vapor

// MARK: - OrgProgram Model
final class OrgProgram: Model, @unchecked Sendable {
    static let schema = "org_programs"
    
    @ID var id: UUID?
    
    @Field(key: "org_id")
    var orgId: String
    
    @Field(key: "program_name")
    var programName: String
    
    @Field(key: "program_key")
    var programKey: String
    
    @Field(key: "program_config")
    var programConfig: ProgramConfig
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         orgId: String,
         programName: String,
         programKey: String,
         programConfig: ProgramConfig) {
        self.id = id
        self.orgId = orgId
        self.programName = programName
        self.programKey = programKey
        self.programConfig = programConfig
    }
}

// MARK: - Program Configuration Structure
struct ProgramConfig: Codable {
    let orgId: String
    let templateId: String
    let programType: String
    let displayName: String
    let description: String
    let reviewFrequencyDays: Int
    let daysToCompleteAssessment: Int
    let requiredAssessments: [AssessmentItem]
    let programReviews: [ReviewItem]
}

struct AssessmentItem: Codable {
    let itemKey: String
    let title: String
    let required: Bool
}

struct ReviewItem: Codable {
    let itemKey: String
    let title: String
    let required: Bool
}

// MARK: - Content Conformance
extension OrgProgram: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case orgId = "org_id"
        case programName = "program_name"
        case programKey = "program_key"
        case programConfig = "program_config"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Request/Response DTOs
struct OrgProgramCreateRequest: Content {
    let programName: String
    let programKey: String
    let programConfig: ProgramConfig
}

struct OrgProgramUpdateRequest: Content {
    let programName: String?
    let programConfig: ProgramConfig?
}

struct OrgProgramResponse: Content {
    let id: UUID?
    let orgId: String
    let programName: String
    let programKey: String
    let programConfig: ProgramConfig
    let createdAt: Date?
    let updatedAt: Date?
    
    init(orgProgram: OrgProgram) {
        self.id = orgProgram.id
        self.orgId = orgProgram.orgId
        self.programName = orgProgram.programName
        self.programKey = orgProgram.programKey
        self.programConfig = orgProgram.programConfig
        self.createdAt = orgProgram.createdAt
        self.updatedAt = orgProgram.updatedAt
    }
}

// MARK: - Migration
struct CreateOrgProgram: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("org_programs")
            .id()
            .field("org_id", .string, .required)
            .field("program_name", .string, .required)
            .field("program_key", .string, .required)
            .field("program_config", .json, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .unique(on: "org_id", "program_key")
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("org_programs").delete()
    }
}
