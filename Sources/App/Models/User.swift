import Fluent
import Vapor

final class User: Model, Content, @unchecked Sendable {
    static let schema = "users"
    
    @ID var id: UUID?
    
    @Field(key: "email")
    var email: String
    
    @Field(key: "firstName")
    var firstName: String
    
    @OptionalField(key: "middleName")
    var middleName: String?
    
    @Field(key: "lastName")
    var lastName: String
    
    @OptionalField(key: "auth")
    var auth: String?
    
    @OptionalField(key: "profile")
    var profile: String?
    
    @OptionalField(key: "color")
    var color: String?
    
    @OptionalField(key: "roles")
    var roles: [String]?
    
    @Children(for: \.$user)
    var attachments: [Attachment]
    
    @Siblings(through: UserTeams.self, from: \.$user, to: \.$team)
    public var teams: [Team]
    
    @Siblings(through: UserChats.self, from: \.$user, to: \.$chat)
    public var chats: [Chat]
    
    @OptionalChild(for: \.$creator)
    var noteCreator: Note?
    
    @OptionalChild(for: \.$creator)
    var chatCreator: Chat?
    
    @OptionalChild(for: \.$latestMessageSender)
    var chatSender: Chat?
    
    @OptionalChild(for: \.$creator)
    var reason: Reason?
    
    @OptionalChild(for: \.$taker)
    var survey: Survey?
    
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @OptionalChild(for: \.$host)
    var appointment: Appointment?            
    
    @Children(for: \.$user)
    var phones: [PhoneNumber]
    
    @OptionalChild(for: \.$creator)
    var taskCreator: TaskModel?
    
    @OptionalChild(for: \.$assignee)
    var taskAssignee: TaskModel?
    
    @OptionalField(key: "meta")
    var meta: MetaData?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:         UUID?    = nil,
         email:      String,
         firstName:  String,
         middleName: String?  = nil,
         lastName:   String,
         auth:       String?  = nil,
         profile:    String?  = nil,
         color:      String?  = nil,
         roles:     [String]? = nil,
         meta:       MetaData? = nil
         ) {
        self.id          = id
        self.email       = email
        self.firstName   = firstName
        self.middleName  = middleName
        self.lastName    = lastName
        self.auth        = auth
        self.profile     = profile
        self.color       = color
        self.roles       = roles
        self.meta        = meta
    }
    
    func profileUrl() -> String {
        if let profile {
            return profile
        } else {
            return self.attachments.first(where: {$0.kind == "profile"})?.url ?? ""
        }
    }
    
    func fullName() -> String {
        return "\(firstName) \(lastName)"
    }
}

struct UserMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(User.schema)
            .id()
            .field("email",      .string, .required)
            .field("firstName",  .string, .required)
            .field("middleName", .string)
            .field("lastName",   .string, .required)
            .field("auth",       .string)
            .field("profile",    .string)
            .field("color",      .string)
            .field("roles",      .array(of: .string))
        
        
            .field("org_id",     .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(User.schema).delete()
    }
}

extension User {    
    func memberInfo() -> (fullName: String, identity: String) {
        let fullName = "\(self.firstName) \(self.lastName)"
        let identity = "\(self.firstName)-\(self.lastName)"
        return (fullName, identity)
    }
}


struct UserMetaMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(User.schema)
            .field("meta", .json)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(User.schema).delete()
    }
}


struct UserAccessMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        let metaData = MetaData(data: [:], access: Accesskeys.default)
        return User.query(on: database)
            .set(\.$meta, to: metaData)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(User.schema).delete()
    }
}


extension User {
    func isAdminRole() -> Bool {
        // Check if user has admin role
        if let roles = roles,
            roles.contains("drop_all") ||
            roles.contains(where: { $0.localizedCaseInsensitiveContains("admin") }) {
            return true
        } else {
            return false
        }
    }
}
