//
//  BackgroundCheckReviewQueue.swift
//  
//
//  Created by <PERSON> on 9/9/25.
//

import Foundation
import Fluent
import Vapor

// MARK: - Background Check Target Type
enum BackgroundCheckTargetType: String, Codable, CaseIterable {
    case member = "member"
    case household = "household"
}

final class BackgroundCheckReviewQueue: Model, Content, @unchecked Sendable {
    static let schema = "background_check_review_queue"

    @ID var id: UUID?

    // Optional relationships - either member OR household
    @OptionalParent(key: "member_id")
    var member: Member?

    @OptionalParent(key: "household_id")
    var household: Household?

    @Field(key: "target_type")
    var targetType: BackgroundCheckTargetType

    @Parent(key: "triggered_by")
    var triggeredBy: User
    
    @Field(key: "full_name")
    var fullName: String
    
    @Field(key: "zip_code")
    var zipCode: String
    
    @Field(key: "raw_response")
    var rawResponse: ZylaOffenderResponse
    
    @Field(key: "matched_count")
    var matchedCount: Int
    
    @OptionalField(key: "highest_risk_level")
    var highestRiskLevel: String?
    
    @Field(key: "requires_review")
    var requiresReview: Bool
    
    @Field(key: "review_status")
    var reviewStatus: ReviewStatus
    
    @OptionalParent(key: "reviewed_by")
    var reviewedBy: User?
    
    @OptionalField(key: "reviewed_at")
    var reviewedAt: Date?

    @OptionalField(key: "review_notes")
    var reviewNotes: String?

    @OptionalField(key: "selected_offender_index")
    var selectedOffenderIndex: Int?

    @OptionalField(key: "match_selections")
    var matchSelections: [MatchSelection]?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() {}

    // Member-specific background check initializer
    init(id: UUID? = nil,
         memberId: UUID,
         triggeredById: UUID,
         fullName: String,
         zipCode: String,
         rawResponse: ZylaOffenderResponse,
         matchedCount: Int,
         highestRiskLevel: String? = nil,
         requiresReview: Bool = true,
         reviewStatus: ReviewStatus = .pending,
         reviewNotes: String? = nil,
         selectedOffenderIndex: Int? = nil,
         matchSelections: [MatchSelection]? = nil) {
        self.id = id
        self.$member.id = memberId
        self.$household.id = nil
        self.targetType = .member
        self.$triggeredBy.id = triggeredById
        self.fullName = fullName
        self.zipCode = zipCode
        self.rawResponse = rawResponse
        self.matchedCount = matchedCount
        self.highestRiskLevel = highestRiskLevel
        self.requiresReview = requiresReview
        self.reviewStatus = reviewStatus
        self.reviewNotes = reviewNotes
        self.selectedOffenderIndex = selectedOffenderIndex
        self.matchSelections = matchSelections
    }

    // Household-specific background check initializer
    init(id: UUID? = nil,
         householdId: UUID,
         triggeredById: UUID,
         fullName: String,
         zipCode: String,
         rawResponse: ZylaOffenderResponse,
         matchedCount: Int,
         highestRiskLevel: String? = nil,
         requiresReview: Bool = true,
         reviewStatus: ReviewStatus = .pending,
         reviewNotes: String? = nil,
         selectedOffenderIndex: Int? = nil,
         matchSelections: [MatchSelection]? = nil) {
        self.id = id
        self.$member.id = nil
        self.$household.id = householdId
        self.targetType = .household
        self.$triggeredBy.id = triggeredById
        self.fullName = fullName
        self.zipCode = zipCode
        self.rawResponse = rawResponse
        self.matchedCount = matchedCount
        self.highestRiskLevel = highestRiskLevel
        self.requiresReview = requiresReview
        self.reviewStatus = reviewStatus
        self.reviewNotes = reviewNotes
        self.selectedOffenderIndex = selectedOffenderIndex
        self.matchSelections = matchSelections
    }
}

// MARK: - Match Selection
struct MatchSelection: Codable {
    let offenderIndex: Int
    let isSelected: Bool
    let isCorrectPerson: Bool
    let adminNotes: String?

    init(offenderIndex: Int, isSelected: Bool, isCorrectPerson: Bool, adminNotes: String? = nil) {
        self.offenderIndex = offenderIndex
        self.isSelected = isSelected
        self.isCorrectPerson = isCorrectPerson
        self.adminNotes = adminNotes
    }
}

// MARK: - Location-Based Search Structures
struct ZylaLocationResponse: Content, Codable {
    let exactMatches: [ZylaOffenderRecord]
    let nearbyOffenders: [ZylaOffenderRecord]
    let searchQuery: ZylaLocationQuery
    let timestamp: Date
    let totalCount: Int

    init(exactMatches: [ZylaOffenderRecord], nearbyOffenders: [ZylaOffenderRecord], searchQuery: ZylaLocationQuery, timestamp: Date = Date()) {
        self.exactMatches = exactMatches
        self.nearbyOffenders = nearbyOffenders
        self.searchQuery = searchQuery
        self.timestamp = timestamp
        self.totalCount = exactMatches.count + nearbyOffenders.count
    }
}

struct ZylaLocationQuery: Content, Codable {
    let address: String
    let latitude: Double
    let longitude: Double
    let radiusMiles: Double

    enum CodingKeys: String, CodingKey {
        case address
        case latitude
        case longitude
        case radiusMiles = "radius_miles"
    }
}

// MARK: - Zyla API Response Structure
struct ZylaOffenderResponse: Content, Codable {
    let results: [ZylaOffenderRecord]
    let searchQuery: ZylaSearchQuery
    let timestamp: Date
    
    init(results: [ZylaOffenderRecord], searchQuery: ZylaSearchQuery, timestamp: Date = Date()) {
        self.results = results
        self.searchQuery = searchQuery
        self.timestamp = timestamp
    }
}

struct ZylaSearchQuery: Content, Codable {
    let name: String
    let zipCode: String
    
    enum CodingKeys: String, CodingKey {
        case name
        case zipCode = "zip_code"
    }
}

struct ZylaOffenderRecord: Content, Codable {
    let name: String
    let aliases: String?
    let address: String?
    let city: String?
    let state: String?
    let zipCode: String?
    let location: String?
    let riskLevel: String?
    let gender: String?
    let age: String?
    let eyeColor: String?
    let hairColor: String?
    let height: String?
    let weight: String?
    let marksScarsTattoos: String?
    let race: String?
    let courtRecord: String?
    let photoUrl: String?
    let updateDatetime: String?

    enum CodingKeys: String, CodingKey {
        case name
        case aliases
        case address
        case city
        case state
        case zipCode = "zip_code"
        case location
        case riskLevel = "risk_level"
        case gender
        case age
        case eyeColor = "eye_color"
        case hairColor = "hair_color"
        case height
        case weight
        case marksScarsTattoos = "marks_scars_tattoos"
        case race
        case courtRecord = "court_record"
        case photoUrl = "photo_url"
        case updateDatetime = "update_datetime"
    }
    
}

// MARK: - Migration
struct CreateBackgroundCheckReviewQueueMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(BackgroundCheckReviewQueue.schema)
            .id()
            .field("member_id", .uuid, .required, .references(Member.schema, "id", onDelete: .cascade))
            .field("triggered_by", .uuid, .required, .references(User.schema, "id", onDelete: .cascade))
            .field("full_name", .string, .required)
            .field("zip_code", .string, .required)
            .field("raw_response", .json, .required)
            .field("matched_count", .int, .required)
            .field("highest_risk_level", .string)
            .field("requires_review", .bool, .required)
            .field("review_status", .string, .required)
            .field("reviewed_by", .uuid, .references(User.schema, "id", onDelete: .setNull))
            .field("reviewed_at", .datetime)
            .field("review_notes", .string)
            .field("selected_offender_index", .int)
            .field("match_selections", .json)
            .field("created_at", .datetime, .required)
            .field("updated_at", .datetime, .required)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(BackgroundCheckReviewQueue.schema).delete()
    }
}

// MARK: - Extensions
extension BackgroundCheckReviewQueue {
    
    /// Get the highest risk level from the raw response
    func calculateHighestRiskLevel() -> String? {
        let riskLevels = rawResponse.results.compactMap { $0.riskLevel }
        
        // Priority: Level III > Level II > Level I
        if riskLevels.contains(where: { $0.contains("III") }) {
            return "Level III"
        } else if riskLevels.contains(where: { $0.contains("II") }) {
            return "Level II"
        } else if riskLevels.contains(where: { $0.contains("I") }) {
            return "Level I"
        }
        
        return riskLevels.first
    }
    
    /// Check if this record requires review based on matches found
    func shouldRequireReview() -> Bool {
        return matchedCount > 0
    }
    
    /// Generate security flags from the raw response
    func generateSecurityFlags() -> [SecurityFlag] {
        return rawResponse.results.compactMap { record in
            guard let riskLevel = record.riskLevel,
                  let courtRecord = record.courtRecord else {
                return nil
            }
            
            return SecurityFlag(
                type: "offender_registry",
                riskLevel: riskLevel,
                convictionSummary: courtRecord,
                convictionDate: extractConvictionDate(from: courtRecord),
                statute: extractStatute(from: courtRecord),
                jurisdiction: record.state
            )
        }
    }
    
    private func extractConvictionDate(from courtRecord: String) -> String? {
        // Simple regex to extract date patterns - can be enhanced
        let datePattern = #"\d{4}-\d{2}-\d{2}"#
        if let range = courtRecord.range(of: datePattern, options: .regularExpression) {
            return String(courtRecord[range])
        }
        return nil
    }
    
    private func extractStatute(from courtRecord: String) -> String? {
        // Extract statute numbers like "9A.44.083"
        let statutePattern = #"\d+[A-Z]?\.\d+\.\d+"#
        if let range = courtRecord.range(of: statutePattern, options: .regularExpression) {
            return String(courtRecord[range])
        }
        return nil
    }
}
