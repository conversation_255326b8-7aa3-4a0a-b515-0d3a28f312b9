//
//  SecurityProfile.swift
//  
//
//  Created by <PERSON> on 9/9/25.
//

import Foundation
import Fluent
import Vapor

// MARK: - Security Profile Structures
struct SecurityProfile: Content, Codable {
    var backgroundChecks: [BackgroundCheck]
    var clearanceStatus: String?
    var incidentReports: [IncidentReport]?
    var safetyPlan: SafetyPlan?
    
    enum CodingKeys: String, CodingKey {
        case backgroundChecks = "background_checks"
        case clearanceStatus = "clearance_status"
        case incidentReports = "incident_reports"
        case safetyPlan = "safety_plan"
    }
    
    init() {
        self.backgroundChecks = []
        self.clearanceStatus = nil
        self.incidentReports = []
        self.safetyPlan = nil
    }
    
    init(backgroundChecks: [BackgroundCheck] = [],
         clearanceStatus: String? = nil,
         incidentReports: [IncidentReport]? = [],
         safetyPlan: SafetyPlan? = nil) {
        self.backgroundChecks = backgroundChecks
        self.clearanceStatus = clearanceStatus
        self.incidentReports = incidentReports
        self.safetyPlan = safetyPlan
    }
}

// MARK: - Background Check Structure
struct BackgroundCheck: Content, Codable {
    let checkedAt: Date
    let status: String // "clear", "flagged", "pending"
    let matchType: String? // "verified_match", "possible_match", "no_match"
    let reviewStatus: String // "pending", "approved", "rejected"
    let reviewedBy: UUID?
    let source: String // "Zyla Offender Registry API"
    let flags: [SecurityFlag]?
    let requiresReview: Bool
    
    enum CodingKeys: String, CodingKey {
        case checkedAt = "checked_at"
        case status
        case matchType = "match_type"
        case reviewStatus = "review_status"
        case reviewedBy = "reviewed_by"
        case source
        case flags
        case requiresReview = "requires_review"
    }
    
    init(checkedAt: Date = Date(),
         status: String,
         matchType: String? = nil,
         reviewStatus: String = "pending",
         reviewedBy: UUID? = nil,
         source: String = "Zyla Offender Registry API",
         flags: [SecurityFlag]? = nil,
         requiresReview: Bool = false) {
        self.checkedAt = checkedAt
        self.status = status
        self.matchType = matchType
        self.reviewStatus = reviewStatus
        self.reviewedBy = reviewedBy
        self.source = source
        self.flags = flags
        self.requiresReview = requiresReview
    }
}

// MARK: - Security Flag Structure
struct SecurityFlag: Content, Codable {
    let type: String // "offender_registry", "criminal_background", etc.
    let riskLevel: String? // "Level I", "Level II", "Level III"
    let convictionSummary: String?
    let convictionDate: String?
    let statute: String?
    let jurisdiction: String?
    
    enum CodingKeys: String, CodingKey {
        case type
        case riskLevel = "risk_level"
        case convictionSummary = "conviction_summary"
        case convictionDate = "conviction_date"
        case statute
        case jurisdiction
    }
    
    init(type: String,
         riskLevel: String? = nil,
         convictionSummary: String? = nil,
         convictionDate: String? = nil,
         statute: String? = nil,
         jurisdiction: String? = nil) {
        self.type = type
        self.riskLevel = riskLevel
        self.convictionSummary = convictionSummary
        self.convictionDate = convictionDate
        self.statute = statute
        self.jurisdiction = jurisdiction
    }
}

// MARK: - Incident Report Structure (Future Use)
struct IncidentReport: Content, Codable {
    let id: UUID?
    let reportedAt: Date
    let type: String
    let description: String
    let reportedBy: UUID
    
    enum CodingKeys: String, CodingKey {
        case id
        case reportedAt = "reported_at"
        case type
        case description
        case reportedBy = "reported_by"
    }
}

// MARK: - Safety Plan Structure (Future Use)
struct SafetyPlan: Content, Codable {
    let lastUpdated: Date
    let requiresReview: Bool
    let notes: String?
    
    enum CodingKeys: String, CodingKey {
        case lastUpdated = "last_updated"
        case requiresReview = "requires_review"
        case notes
    }
}

// MARK: - Review Status Enum
enum ReviewStatus: String, CaseIterable, Content {
    case pending = "pending"
    case approved = "approved"
    case rejected = "rejected"
}

// MARK: - Background Check Status Enum
enum BackgroundCheckStatus: String, CaseIterable, Content {
    case clear = "clear"
    case flagged = "flagged"
    case pending = "pending"
}

// MARK: - Match Type Enum
enum MatchType: String, CaseIterable, Content {
    case verifiedMatch = "verified_match"
    case possibleMatch = "possible_match"
    case noMatch = "no_match"
}
