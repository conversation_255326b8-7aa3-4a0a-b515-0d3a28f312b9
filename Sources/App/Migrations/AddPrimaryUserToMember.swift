//
//  AddPrimaryUserToMember.swift
//
//
//  Created by Augment Agent on 8/29/25.
//

import Foundation
import Fluent
import Vapor

struct AddPrimaryUserToMember: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("members")
            .field("primary_user_id", .uuid, .references("users", "id", onDelete: .setNull))
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("members")
            .deleteField("primary_user_id")
            .update()
    }
}
