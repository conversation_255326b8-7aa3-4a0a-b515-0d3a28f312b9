//
//  AddSecurityProfileToMember.swift
//  
//
//  Created by <PERSON> on 9/9/25.
//

import Foundation
import Fluent
import Vapor

struct AddSecurityProfileToMemberMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .field("security_profile", .json)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .deleteField("security_profile")
            .update()
    }
}
