//
//  AddAssignedToIdFields.swift
//  
//
//  Created by Augment Agent on 9/23/25.
//

import Foundation
import Fluent
import Vapor

struct AddAssignedToIdFields: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        // Add assignedToId UUID field to programs table
        let programUpdate = database.schema("programs")
            .field("assigned_to_id", .uuid, .references("users", "id", onDelete: .setNull))
            .update()
        
        // Add assignedToId UUID field to program_tasks table
        let programTaskUpdate = database.schema("program_tasks")
            .field("assigned_to_id", .uuid, .references("users", "id", onDelete: .setNull))
            .update()
        
        // Add assignedToId UUID field to review_periods table
        let reviewPeriodUpdate = database.schema("review_periods")
            .field("assigned_to_id", .uuid, .references("users", "id", onDelete: .setNull))
            .update()
        
        // Execute all updates sequentially
        return programUpdate
            .flatMap { _ in programTaskUpdate }
            .flatMap { _ in reviewPeriodUpdate }
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        // Remove assignedToId fields from all tables
        let programRevert = database.schema("programs")
            .deleteField("assigned_to_id")
            .update()
        
        let programTaskRevert = database.schema("program_tasks")
            .deleteField("assigned_to_id")
            .update()
        
        let reviewPeriodRevert = database.schema("review_periods")
            .deleteField("assigned_to_id")
            .update()
        
        // Execute all reverts sequentially
        return programRevert
            .flatMap { _ in programTaskRevert }
            .flatMap { _ in reviewPeriodRevert }
    }
}
