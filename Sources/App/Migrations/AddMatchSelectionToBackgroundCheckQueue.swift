//
//  AddMatchSelectionToBackgroundCheckQueue.swift
//  
//
//  Created by Augment Agent on 9/9/25.
//

import Foundation
import Fluent
import Vapor

struct AddMatchSelectionToBackgroundCheckQueue: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(BackgroundCheckReviewQueue.schema)
            .field("selected_offender_index", .int)
            .field("match_selections", .json)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(BackgroundCheckReviewQueue.schema)
            .deleteField("selected_offender_index")
            .deleteField("match_selections")
            .update()
    }
}
