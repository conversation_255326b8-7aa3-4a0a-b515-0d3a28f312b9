//
//  AddTitleToDiagnosis.swift
//  
//
//  Created by <PERSON> on 8/22/25.
//

import Foundation
import Fluent

struct AddTitleToDiagnosis: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("diagnoses")
            .field("title", .string)
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("diagnoses")
            .deleteField("title")
            .update()
    }
}
