//
//  AddHouseholdSupportToBackgroundCheckQueue.swift
//  
//
//  Created by Augment Agent on 9/22/25.
//

import Foundation
import Fluent
import Vapor

struct AddHouseholdSupportToBackgroundCheckQueue: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(BackgroundCheckReviewQueue.schema)
            // Add household_id field
            .field("household_id", .uuid, .references(Household.schema, "id", onDelete: .cascade))
            // Add target_type field with default value "member" for existing records
            .field("target_type", .string, .required, .custom("DEFAULT 'member'"))
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(BackgroundCheckReviewQueue.schema)
            .deleteField("household_id")
            .deleteField("target_type")
            .update()
    }
}
