//
//  HTTPMethodOverrideMiddleware.swift
//
//
//  Created by Augment Agent on 9/1/25.
//

import Foundation
import Vapor

/// Middleware that allows HTML forms to override HTTP methods using a hidden _method field
/// This enables forms to simulate DELETE, PUT, PATCH requests by sending POST with _method parameter
struct HTTPMethodOverrideMiddleware: Middleware {

    /// The form field name to check for method override (default: "_method")
    private let fieldName: String

    /// Supported HTTP methods that can be overridden (using string comparison)
    private let supportedMethods: Set<String> = ["DELETE", "PUT", "PATCH"]

    init(fieldName: String = "_method") {
        self.fieldName = fieldName
    }

    func respond(to request: Request, chainingTo next: Responder) -> EventLoopFuture<Response> {
        // Only process POST requests
        guard request.method == .POST else {
            return next.respond(to: request)
        }

        // Check if the request has form data
        guard let contentType = request.headers.contentType,
              contentType.type == "application" && contentType.subType == "x-www-form-urlencoded" else {
            return next.respond(to: request)
        }

        do {
            // Try to decode the form data to check for _method field
            let formData = try request.content.decode([String: String].self)

            // Check if _method field exists and contains a valid override method
            if let methodString = formData[fieldName] {
                let upperMethodString = methodString.uppercased()
                if supportedMethods.contains(upperMethodString) {
                    // Override the request method
                    request.method = HTTPMethod(rawValue: upperMethodString)

                    // Log the method override for debugging
                    request.logger.debug("HTTP Method Override: POST -> \(upperMethodString)")
                }
            }

            return next.respond(to: request)

        } catch {
            // If we can't decode form data, just continue with original request
            request.logger.debug("HTTPMethodOverrideMiddleware: Could not decode form data, continuing with original method")
            return next.respond(to: request)
        }
    }
}
