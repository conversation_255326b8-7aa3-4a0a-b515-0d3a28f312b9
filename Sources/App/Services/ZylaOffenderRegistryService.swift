//
//  ZylaOffenderRegistryService.swift
//
//
//  Created by <PERSON> on 9/9/25.
//

import Foundation
import Vapor
import AsyncHTTPClient

// MARK: - Zyla API Service
struct ZylaOffenderRegistryService {
    private let client: HTTPClient
    private let apiKey: String
    private let baseURL = "https://zylalabs.com/api/2117/offender+registry+usa+api"
    
    init(client: HTTPClient, apiKey: String) {
        self.client = client
        self.apiKey = apiKey
    }
    
    /// Search for offenders by name and zip code
    func searchOffenders(name: String, zipCode: String) async throws -> ZylaOffenderResponse {
        let endpoint = "\(baseURL)/1910/get+offender+by+zip+code+and+name"
        
        // Validate inputs
        guard !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw ZylaAPIError.invalidInput("Name cannot be empty")
        }
        
        guard zipCode.count == 5, zipCode.allSatisfy(\.isNumber) else {
            throw ZylaAPIError.invalidInput("Zip code must be 5 digits")
        }
        
        // Build query parameters
        var urlComponents = URLComponents(string: endpoint)!
        urlComponents.queryItems = [
            URLQueryItem(name: "zipcode", value: zipCode),
            URLQueryItem(name: "name", value: name)
        ]
        
        guard let url = urlComponents.url else {
            throw ZylaAPIError.invalidURL
        }
        
        // Create request
        var request = HTTPClientRequest(url: url.absoluteString)
        request.method = .GET
        request.headers.add(name: "Authorization", value: "Bearer \(apiKey)")
        request.headers.add(name: "Content-Type", value: "application/json")
        
        do {
            let response = try await client.execute(request, timeout: .seconds(30))
            
            // Check status code
            guard response.status == .ok else {
                let errorBody = try await response.body.collect(upTo: 1024 * 1024) // 1MB limit
                let errorMessage = String(buffer: errorBody)
                throw ZylaAPIError.httpError(Int(response.status.code), errorMessage)
            }
            
            // Parse response body
            let body = try await response.body.collect(upTo: 10 * 1024 * 1024) // 10MB limit
            let data = Data(buffer: body)
            
            // Decode the response
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            let rawResults = try decoder.decode([ZylaOffenderRecord].self, from: data)
            
            // Create structured response
            let searchQuery = ZylaSearchQuery(name: name, zipCode: zipCode)
            let zylaResponse = ZylaOffenderResponse(
                results: rawResults,
                searchQuery: searchQuery,
                timestamp: Date()
            )
            
            return zylaResponse
            
        } catch let error as ZylaAPIError {
            throw error
        } catch {
            throw ZylaAPIError.networkError(error.localizedDescription)
        }
    }
    
    /// Analyze the response to determine if it requires review
    func analyzeResponse(_ response: ZylaOffenderResponse) -> (matchedCount: Int, highestRiskLevel: String?, requiresReview: Bool) {
        let matchedCount = response.results.count
        
        // Determine highest risk level
        let riskLevels = response.results.compactMap { $0.riskLevel }
        let highestRiskLevel = determineHighestRiskLevel(from: riskLevels)
        
        // Determine if review is required (any matches found)
        let requiresReview = matchedCount > 0
        
        return (matchedCount, highestRiskLevel, requiresReview)
    }
    
    private func determineHighestRiskLevel(from riskLevels: [String]) -> String? {
        // Priority: Level III > Level II > Level I
        if riskLevels.contains(where: { $0.contains("III") }) {
            return "Level III"
        } else if riskLevels.contains(where: { $0.contains("II") }) {
            return "Level II"
        } else if riskLevels.contains(where: { $0.contains("I") }) {
            return "Level I"
        }
        
        return riskLevels.first
    }
    
    /// Search for offenders by location with 2-mile radius
    func searchOffendersByLocation(address: String, latitude: Double, longitude: Double) async throws -> ZylaLocationResponse {
        // For location-based searches, we'll use the zip code endpoint with the address's zip code
        // and then filter results by proximity to the given coordinates
        let endpoint = "\(baseURL)/1908/get+offenders+by+location"
        
        // Extract zip code from address or use a default search approach
        // For now, we'll search by location coordinates and filter results
        let radiusMiles = 2.0
        
        var urlComponents = URLComponents(string: endpoint)!
        
        
        urlComponents.queryItems = [
            URLQueryItem(name: "lat", value: "\(latitude)"),
            URLQueryItem(name: "lng", value: "\(longitude)"),
            URLQueryItem(name: "radius", value: "\(radiusMiles)")
        ]
        
        guard let url = urlComponents.url else {
            throw ZylaAPIError.invalidURL
        }
        
        // Create request
        var request = HTTPClientRequest(url: url.absoluteString)
        request.method = .GET
        request.headers.add(name: "Authorization", value: "Bearer \(apiKey)")
        request.headers.add(name: "Content-Type", value: "application/json")
        
        do {
            let response = try await client.execute(request, timeout: .seconds(30))
            
            // Check status code
            guard response.status == .ok else {
                let errorBody = try await response.body.collect(upTo: 1024 * 1024) // 1MB limit
                let errorMessage = String(buffer: errorBody)
                throw ZylaAPIError.httpError(Int(response.status.code), errorMessage)
            }
            
            // Parse response body
            let body = try await response.body.collect(upTo: 10 * 1024 * 1024) // 10MB limit
            let data = Data(buffer: body)
            
            // Decode the response
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            let rawResults = try decoder.decode([ZylaOffenderRecord].self, from: data)
            
            // Filter results by distance from the target coordinates
            let (exactMatches, nearbyOffenders) = filterOffendersByLocation(
                offenders: rawResults,
                targetLatitude: latitude,
                targetLongitude: longitude,
                targetAddress: address,
                radiusMiles: radiusMiles
            )
            
            let searchQuery = ZylaLocationQuery(
                address: address,
                latitude: latitude,
                longitude: longitude,
                radiusMiles: radiusMiles
            )
            
            return ZylaLocationResponse(
                exactMatches: exactMatches,
                nearbyOffenders: nearbyOffenders,
                searchQuery: searchQuery,
                timestamp: Date()
            )
            
        } catch let error as ZylaAPIError {
            throw error
        } catch {
            throw ZylaAPIError.networkError(error.localizedDescription)
        }
    }
    /// Extract zip code from address string
    private func extractZipCodeFromAddress(_ address: String) -> String? {
        // Look for 5-digit zip code pattern in the address
        let zipPattern = #"\b\d{5}\b"#
        let regex = try? NSRegularExpression(pattern: zipPattern)
        let range = NSRange(location: 0, length: address.utf16.count)
        
        if let match = regex?.firstMatch(in: address, options: [], range: range) {
            let zipRange = Range(match.range, in: address)
            return zipRange.map { String(address[$0]) }
        }
        
        return nil
    }
    
    private func filterOffendersByLocation(
        offenders: [ZylaOffenderRecord],
        targetLatitude: Double,
        targetLongitude: Double,
        targetAddress: String,
        radiusMiles: Double
    ) -> (exactMatches: [ZylaOffenderRecord], nearbyOffenders: [ZylaOffenderRecord]) {
        var exactMatches: [ZylaOffenderRecord] = []
        var nearbyOffenders: [ZylaOffenderRecord] = []
        
        for offender in offenders {
            // Parse offender location
            guard let location = parseLocation(offender.location) else {
                continue
            }
            
            let distance = calculateDistance(
                lat1: targetLatitude, lon1: targetLongitude,
                lat2: location.latitude, lon2: location.longitude
            )
            
            // Check if it's an exact address match
            if isExactAddressMatch(offenderAddress: offender.address ?? "", targetAddress: targetAddress) {
                exactMatches.append(offender)
            } else if distance <= radiusMiles {
                nearbyOffenders.append(offender)
            }
        }
        
        return (exactMatches, nearbyOffenders)
    }
    
    
    /// Parse location string into coordinates
    private func parseLocation(_ locationString: String?) -> (latitude: Double, longitude: Double)? {
        guard let location = locationString else { return nil }
        
        let components = location.split(separator: ",")
        guard components.count == 2,
              let lat = Double(components[0].trimmingCharacters(in: .whitespaces)),
              let lon = Double(components[1].trimmingCharacters(in: .whitespaces)) else {
            return nil
        }
        
        return (latitude: lat, longitude: lon)
    }
    
    /// Calculate distance between two coordinates in miles
    private func calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double) -> Double {
        let earthRadius = 3959.0 // Earth's radius in miles
        
        let dLat = (lat2 - lat1) * .pi / 180.0
        let dLon = (lon2 - lon1) * .pi / 180.0
        
        let a = sin(dLat/2) * sin(dLat/2) +
        cos(lat1 * .pi / 180.0) * cos(lat2 * .pi / 180.0) *
        sin(dLon/2) * sin(dLon/2)
        
        let c = 2 * atan2(sqrt(a), sqrt(1-a))
        
        return earthRadius * c
    }
    
    /// Check if offender address is an exact match to target address
    private func isExactAddressMatch(offenderAddress: String, targetAddress: String) -> Bool {
        let normalizedOffender = offenderAddress.lowercased()
            .replacingOccurrences(of: "block of ", with: "")
            .replacingOccurrences(of: " block", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        let normalizedTarget = targetAddress.lowercased()
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Check for partial matches in street names
        let offenderComponents = normalizedOffender.components(separatedBy: " ")
        let targetComponents = normalizedTarget.components(separatedBy: " ")
        
        // Look for common street name components
        let commonComponents = Set(offenderComponents).intersection(Set(targetComponents))
        return commonComponents.count >= 2 // At least 2 common words (e.g., street name + type)
    }
    
    /// Categorize offenders into exact address matches vs nearby offenders
    private func categorizeOffendersByAddress(offenders: [ZylaOffenderRecord], searchedAddress: String) -> ([ZylaOffenderRecord], [ZylaOffenderRecord]) {
        var exactMatches: [ZylaOffenderRecord] = []
        var nearbyOffenders: [ZylaOffenderRecord] = []
        
        for offender in offenders {
            if isExactAddressMatch(searchedAddress: searchedAddress, offenderAddress: offender.address, offenderZip: offender.zipCode) {
                exactMatches.append(offender)
            } else {
                nearbyOffenders.append(offender)
            }
        }
        
        return (exactMatches, nearbyOffenders)
    }
    
    /// Check if an offender's address is an exact match to the searched address
    private func isExactAddressMatch(searchedAddress: String, offenderAddress: String?, offenderZip: String?) -> Bool {
        guard let offenderAddress = offenderAddress,
              let offenderZip = offenderZip else {
            return false
        }
        
        // Normalize addresses for comparison
        let normalizedSearched = normalizeAddress(searchedAddress)
        let normalizedOffender = normalizeAddress(offenderAddress)
        
        // Check if addresses are similar and zip codes match
        return addressesSimilar(normalizedSearched, normalizedOffender) &&
        zipCodesMatch(searchedAddress, offenderZip)
    }
    
    /// Normalize address for comparison
    private func normalizeAddress(_ address: String) -> String {
        return address
            .lowercased()
            .replacingOccurrences(of: "block of", with: "")
            .replacingOccurrences(of: "avenue", with: "ave")
            .replacingOccurrences(of: "street", with: "st")
            .replacingOccurrences(of: "road", with: "rd")
            .replacingOccurrences(of: "drive", with: "dr")
            .replacingOccurrences(of: "  ", with: " ")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// Check if two normalized addresses are similar
    private func addressesSimilar(_ address1: String, _ address2: String) -> Bool {
        // Extract street numbers and names for comparison
        let components1 = address1.components(separatedBy: " ").filter { !$0.isEmpty }
        let components2 = address2.components(separatedBy: " ").filter { !$0.isEmpty }
        
        // If both have street numbers, they should match
        if let num1 = components1.first?.filter(\.isNumber),
           let num2 = components2.first?.filter(\.isNumber),
           !num1.isEmpty && !num2.isEmpty {
            return num1 == num2
        }
        
        // Fallback to basic string similarity
        return address1.contains(address2) || address2.contains(address1)
    }
    
    /// Extract and compare zip codes
    private func zipCodesMatch(_ searchedAddress: String, _ offenderZip: String) -> Bool {
        // Extract zip code from searched address if present
        let zipPattern = "\\b\\d{5}\\b"
        let regex = try? NSRegularExpression(pattern: zipPattern)
        let range = NSRange(searchedAddress.startIndex..<searchedAddress.endIndex, in: searchedAddress)
        
        if let match = regex?.firstMatch(in: searchedAddress, range: range) {
            let searchedZip = String(searchedAddress[Range(match.range, in: searchedAddress)!])
            return searchedZip == offenderZip
        }
        
        return false
    }
}

// MARK: - Zyla API Errors
enum ZylaAPIError: Error, LocalizedError {
    case invalidInput(String)
    case invalidURL
    case httpError(Int, String)
    case networkError(String)
    case decodingError(String)
    case rateLimitExceeded
    case unauthorized
    case serviceUnavailable
    
    var errorDescription: String? {
        switch self {
        case .invalidInput(let message):
            return "Invalid input: \(message)"
        case .invalidURL:
            return "Invalid URL for API request"
        case .httpError(let code, let message):
            return "HTTP error \(code): \(message)"
        case .networkError(let message):
            return "Network error: \(message)"
        case .decodingError(let message):
            return "Failed to decode response: \(message)"
        case .rateLimitExceeded:
            return "API rate limit exceeded. Please try again later."
        case .unauthorized:
            return "Unauthorized access to Zyla API. Check your API key."
        case .serviceUnavailable:
            return "Zyla API service is currently unavailable."
        }
    }
}
