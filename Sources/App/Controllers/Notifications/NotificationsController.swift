//
//  File.swift
//  
//
//  Created by <PERSON> on 1/2/24.
//

import Foundation
import Vapor
import Fluent

struct MarkAsReadInput: Content {
    var ids:[String]
    var state: Bool
}

struct DeleteInput: Content {
    var ids:[String]
}

struct NotificationResponse: Content {
    var status: String
}

struct NotificationCreateInput: Content {
    var title: String
    var kind: String
    var message: String
    var read: Bool
    var userID: String
    var meta: MetaData?
    
    func note() -> UserNotification {
        return UserNotification(title: title.lowercased(),
                                kind: kind.lowercased(),
                                message: message.lowercased(),
                                read: read,
                                userID: UUID(uuidString: userID)!,
                                meta: meta)
    }
    
}

struct CountResponse: Content {
    let unread: Int
    let total: Int?
}

struct NotificationsController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let tasks = routes.grouped("notifications")
        tasks.get(use: index)
        tasks.post(use:create)
        tasks.get("count", use: count)
        tasks.post("sms", use: sms)
        tasks.put(use: update)
        tasks.delete(use: delete)
    }
    
    func index(req: Request) throws -> EventLoopFuture<Page<UserNotification>> {
        return self.buildQuery(query: UserNotification.query(on: req.db), req: req).paginate(for: req)
    }
    
    /// GET /notifications/count?userID=<uuid>&kind=<opt>
    func count(req: Request) throws -> EventLoopFuture<CountResponse> {
        // Reuse your filters
        let base = buildQuery(query: UserNotification.query(on: req.db), req: req)
        
        // unread count
        let unreadQuery = buildQuery(
            query: UserNotification.query(on: req.db), req: req
        ).filter(\.$read == false)
        
        return unreadQuery.count().and(base.count())
            .map { unread, total in CountResponse(unread: unread, total: total) }
    }
    
    //MARK: - Create
    func sms(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        return try TwilioController().sendSms(req: req).transform(to: HTTPStatus.accepted)
    }
    
    func create(req: Request) throws -> EventLoopFuture<UserNotification> {
        let input = try req.content.decode(NotificationCreateInput.self)
        let note    = input.note()
        return note.create(on: req.db).transform(to: note)
    }
    
    func delete(req: Request) throws -> EventLoopFuture<NotificationResponse> {
        let input = try req.content.decode(DeleteInput.self)
        let allids = input.ids.compactMap({ UUID(uuidString: $0)})
        return UserNotification.query(on: req.db)
            .filter(\.$id ~~ allids)
            .delete()
            .transform(to: NotificationResponse(status: "complete"))
    }
    
    func update(req: Request) throws -> EventLoopFuture<NotificationResponse> {
        let input = try req.content.decode(MarkAsReadInput.self)
        let allids = input.ids.compactMap({ UUID(uuidString: $0)})
        return UserNotification.query(on: req.db)
            .set(\.$read, to: input.state)
            .filter(\.$id ~~ allids)
            .update()
            .transform(to: NotificationResponse(status: "complete"))
    }
    
    func findAll(req: Request, ids:[String]) throws -> EventLoopFuture<[UserNotification]> {
        let allids = ids.compactMap({ UUID(uuidString: $0)})
        return UserNotification.query(on: req.db).filter(\.$id ~~ allids).all()
    }
    
    //    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<UserNotification>, req:Request) -> QueryBuilder<UserNotification> {
        let read:String?             = req.query["read"]
        let kind:String?           = req.query["kind"]
        let userID:String?         = req.query["userID"]
        
        if let userID = userID, 
            let uuid = UUID(uuidString: userID) {
            query.filter(\.$userID == uuid)
        }
        
        if let readString = read, 
            let readBool = Bool(readString) {
            query.filter(\.$read == readBool)
        }
        
        if let kind = kind {
            query.filter(\.$kind == kind)
        }
                
        return query.sort(\.$createdAt, .descending)
    }
    
    static func notification(req:Request, input: NotificationCreateInput) throws -> EventLoopFuture<Void> {
        let note = input.note()
        return note.create(on: req.db)
    }
}
