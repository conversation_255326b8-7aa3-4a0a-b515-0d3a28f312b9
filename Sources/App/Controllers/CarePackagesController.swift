//
//  File.swift
//
//
//  Created by <PERSON> on 2/5/23.
//

import Foundation
import Vapor
import Fluent


struct CarePackagesController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("carepackage")
        
        users.get(use: index)
        users.get(":cpID", use: lookup)
        users.get([":cpID","timeline"], use: timeline)
        
        users.post(use: create)
        users.post([":cpID", "item"], use: createCarePackageItem)
        
        users.put([":cpID", "item",":itemID"], use: updateCarePackageItem)
        users.put([":cpID", "end"], use: endCarePackage)
        users.put(":cpID", use: update)
        
        users.delete(":cpID", use: delete)
        users.delete([":cpID", "item",":itemID"], use: deleteItem)
        users.post([":cpID", "networks"], use: deleteNetworksItems)
    }
    
    
    //MARK: -  Fetches
    func timeline(req: Request) throws -> EventLoopFuture<Page<TimelineItem>> {
        guard let id = req.parameters.get("cpID")?.lowercased() else { throw NetworkError.error(type: .carepackage) }
        let status:String?           = req.query["status"]
        let query = TimelineItem.query(on: req.db)
            .filter(\.$carepackageID == id)
            .with(\.$creator) { user in
                user.with(\.$attachments)
            }
            .with(\.$services)
            .with(\.$item) { item in
                item.with(\.$appointments)
                item.with(\.$network) { network in
                    network.with(\.$services)
                }
            }
            .sort(\.$createdAt, .descending)
        if let sts = status {
            query.filter(\.$status == sts.lowercased())
        }
        return query.paginate(for: req)
    }
    
    func lookup(req: Request) throws -> EventLoopFuture<CarePackage> {
        guard let id = req.parameters.get("cpID") else { throw NetworkError.error(type: .carepackage) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .carepackage)}
        return CarePackage.query(on: req.db)
            .filter(\.$id == networkID)
            .with(\.$org)
            .with(\.$items, { item in
                item.with(\.$appointments)
                item.with(\.$network, { network in
                    network
                        .with(\.$address)
                        .with(\.$services)
                })
            })
            .with(\.$reason)
            .first().flatMapThrowing() {model in
                guard let foundModel = model else { throw NetworkError.error(type: .carepackage) }
                return foundModel
            }
    }
    
    static func findAll(req: Request, ids:[String]) throws -> EventLoopFuture<[CarePackage]> {
        let allids = ids.compactMap({ UUID(uuidString: $0)})
        return CarePackage.query(on: req.db).filter(\.$id ~~ allids).all()
    }
    
    static func find(req: Request, id:String?) throws -> EventLoopFuture<CarePackage> {
        guard let id = id else { throw NetworkError.error(type: .carepackage)}
        guard let serviceID = UUID(id) else { throw NetworkError.error(type: .carepackage)}
        return CarePackage.find(serviceID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .carepackage) }
            return foundModel
        }
    }
    
    static func findCarePackageItem(req: Request, id:String?) throws -> EventLoopFuture<CarePackageItem> {
        guard let id = id else { throw NetworkError.error(type: .carepackage)}
        guard let serviceID = UUID(id) else { throw NetworkError.error(type: .carepackage)}
        return CarePackageItem.query(on: req.db).filter(\.$id == serviceID)
            .with(\.$appointments)
            .with(\.$network)
            .first().unwrap(or: NetworkError.error(type: .carepackage))
    }
    
    func index(req: Request) throws -> EventLoopFuture<Page<CarePackage>> {
        let org:String?            = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return self.buildQuery(query: CarePackage.query(on: req.db), req: req, org: orgID).paginate(for: req)
    }
    
    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<CarePackage> {
        let input = try req.content.decode(CarePackageInput.self)
        let package = input.package()
        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in
            return org.$packages.create(package, on: req.db).transform(to: package).flatMap { care in
                return try! addCreatorIfNeeded(req: req, input: input, package: care).flatMap { care in
                    return try! addReciverIfNeeded(req: req, input: input, package: care)
                }
            }
        }
    }
    
    func createCarePackageItem(req: Request) throws -> EventLoopFuture<CarePackage> {
        guard let id = req.parameters.get("cpID") else { throw NetworkError.error(type: .carepackage) }
        let input = try req.content.decode(CarePackageInput.self)
        guard let itemInput  = input.item else { throw NetworkError.error(type: .badRequest, msg: "Item is required") }
        guard let networkID  = itemInput.networkID else { throw NetworkError.error(type: .badRequest, msg: "NetworkID is required") }
        guard let creatorID  = itemInput.creatorID else { throw NetworkError.error(type: .badRequest, msg: "Creator ID is required") }
        
        return try CarePackagesController.find(req: req, id: id).flatMap { package in
            
            return try! NetworksController.find(req: req, id: networkID).flatMap { network in
                
                return try! UsersController.find(req: req, id: creatorID).flatMap { user in
                    
                    let model = CarePackageItem(type: itemInput.type, status: "pending", desc: "")
                    
                    return package.$items.create(model, on: req.db).transform(to: model).flatMap { item in
                        item.$network.id = network.id
                        
                        return item.update(on: req.db).transform(to: item).flatMap { updatedItem in
                            
                            return try! addTimelineItems(req: req, package: package, user: user, network: network, item: updatedItem).transform(to: package)
                            
                        }
                    }
                }
            }
        }
    }
    
    fileprivate func addTimelineItems(req:Request, package: CarePackage, user: User, network: Network, item: CarePackageItem) throws -> EventLoopFuture<Void> {
        let timelineItem = TimeLineItemMessage.carepackage(
            status: package.status == "active" ? "added" : "pending",
            package: package,
            user: user,
            network: network).toTimelineItem()
        
        let timelineItems: [TimelineItem] = [ timelineItem ]
        
        return item.$items.create(timelineItems, on: req.db)
            .transform(to: timelineItems)
            .flatMap { updatedTimeLineItems in
                return updatedTimeLineItems.sequencedFlatMapEach(on: req.eventLoop) { item in
                    item.$creator.id = user.id
                    return item.update(on: req.db)
                }
            }
    }    
    
    //MARK: - Update
    func endCarePackage(req: Request) throws -> EventLoopFuture<CarePackage> {
        guard let id = req.parameters.get("cpID") else { throw NetworkError.error(type: .carepackage) }
        let input = try req.content.decode(EndCarePackageInput.self)
        let reason = input.model()
        reason.$creator.id = UUID(uuidString: input.creatorID)
        return reason.create(on: req.db).transform(to: reason).flatMap { reason in
            return try! UsersController.find(req: req, id: input.creatorID).flatMap { user in
                return try! self.findCarePackage(req: req, id: id).flatMap { package in
                    package.endedAt = Date()
                    package.status = "complete"
                    package.$reason.id = reason.id
                    return package.update(on: req.db).transform(to: package).flatMap { package in
                        
                        let timelineItem = TimeLineItemMessage.endCarepackage(package: package, user: user, reason: reason).toTimelineItem()
                        
                        return  timelineItem.create(on: req.db).transform(to: timelineItem).flatMap { timeline in
                            timeline.$creator.id = UUID(uuidString: input.creatorID)
                            return timeline.update(on: req.db).transform(to: package)
                        }
                    }
                }
            }
        }
    }
    
    fileprivate func createTimelineItem(req:Request, item:TimelineItem, creatorID:String, carePackageItem:CarePackageItem) throws -> EventLoopFuture<CarePackageItem> {
        return try! UsersController.find(req: req, id: creatorID).flatMap { user in
            
            item.$creator.id = user.id
            carePackageItem.status = item.status
            
            return carePackageItem.update(on: req.db).transform(to: carePackageItem).flatMap {  carePackageItem in
                
                return carePackageItem.$items.create(item, on: req.db).transform(to: item).flatMap { timelineItem in
                    
                    return req.eventLoop.future(carePackageItem)
                    
                }
            }
        }
    }
    
    func updateCarePackageItem(req: Request) throws -> EventLoopFuture<CarePackageItem> {
        //CPID used for adding timeline to carepackage.
        guard let cpID = req.parameters.get("cpID")?.lowercased() else { throw NetworkError.error(type: .carepackage) }
        guard let carepackageItemID = req.parameters.get("itemID")?.lowercased() else { throw NetworkError.error(type: .carepackageItem) }
        let input = try req.content.decode(CarePackageItemInput.self)
        guard let itemsInput = input.item else { throw NetworkError.error(type: .badRequest, msg: "Item is required") }
      
        
        return try! CarePackagesController.findCarePackageItem(req: req, id: carepackageItemID).flatMap { carePackageItem in
            
            return try! CarePackagesController().findCarePackage(req: req, id: cpID).flatMap({ package in
                
                if let memID = input.creatorMemID {
                    return try! MembersController.find(req: req, id: memID).flatMap { member in
                        
                        let org = package.org?.id?.uuidString.lowercased() ?? ""
                        let timelineItem = TimeLineItemMessage.updateCarePackage(status: itemsInput.status,
                                                                                 package: package,
                                                                                 input: input).toTimelineItem()
                        
                        carePackageItem.status = (itemsInput.status == "canceled") ? "pending" : itemsInput.status
                        carePackageItem.desc   = itemsInput.desc                        
                        
                        return try! updateAppointmentIfNeed(req: req,
                                                            org: org,
                                                            creator: memID,
                                                            member: package.reciever ?? "",
                                                            item: carePackageItem,
                                                            input: input,
                                                            carepackageId: package.id?.uuidString ?? "").flatMap({ _ in
                            
                            return try! updateCarePackageItem(req: req,
                                                             carePackageItem: carePackageItem,
                                                             timelineItem: timelineItem,
                                                             itemsInput: itemsInput)
                        })
                    }
                }
                else if let creatorID  = input.creatorID {
                    return try! UsersController.find(req: req, id: creatorID).flatMap { user in
                        let org = package.org?.id?.uuidString.lowercased() ?? ""
                        let timelineItem = TimeLineItemMessage.updateCarePackage(status: itemsInput.status,
                                                                                 package: package,
                                                                                 input: input).toTimelineItem()
                        timelineItem.$creator.id = user.id
                        
                        carePackageItem.status = timelineItem.status
                        carePackageItem.desc   = timelineItem.desc
                        
                        return try! updateAppointmentIfNeed(req: req,
                                                            org: org,
                                                            creator: creatorID,
                                                            member: package.reciever ?? "",
                                                            item: carePackageItem,
                                                            input: input,
                                                            carepackageId: package.id?.uuidString ?? "").flatMap({ _ in
                            
                            return try! updateCarePackageItem(req: req,
                                                             carePackageItem: carePackageItem,
                                                             timelineItem: timelineItem,
                                                             itemsInput: itemsInput)
                        })
                    }
                } 
                else {
                    return req.eventLoop.future(carePackageItem)
                }
            })
        }
    }
    
    fileprivate func updateCarePackageItem(req: Request, 
                                           carePackageItem: CarePackageItem,
                                           timelineItem: TimelineItem,
                                           itemsInput: TimelineItemInput) throws -> EventLoopFuture<CarePackageItem> {
        return carePackageItem.update(on: req.db).transform(to: carePackageItem).flatMap {  carePackageItem in
            
            return carePackageItem.$items.create(timelineItem, on: req.db).transform(to: timelineItem).flatMap { timelineItem in
                
                if let services = itemsInput.services {
                    
                    if services.isEmpty {
                        return timelineItem.$services.detachAll(on: req.db).transform(to: carePackageItem)
                    } else {
                        return try! ServicesController.findAll(req: req, ids: services).flatMap { allServices in
                            return timelineItem.$services.detachAll(on: req.db).flatMap { _ in
                                return timelineItem.$services.attach(allServices, on: req.db).transform(to: carePackageItem)
                            }
                        }
                    }
                    
                } else {
                    
                    return req.eventLoop.future(carePackageItem)
                }
            }
        }
    }
    
    func updateAppointmentIfNeed(req: Request,
                                 org: String,
                                 creator: String,
                                 member: String,
                                 item: CarePackageItem,
                                 input: CarePackageItemInput,
                                 carepackageId: String) throws -> EventLoopFuture<Void> {
        guard let timelineItem = input.item else { return req.eventLoop.future() }
        let exsistingAppointment = item.appointments.last
        
        if let exsistingAppointment  {
            guard let aptId = exsistingAppointment.id?.uuidString.lowercased() else {return req.eventLoop.future() }
            return try AppointmentsController.find(req: req, id: aptId).flatMap { appointment in
                appointment.status = timelineItem.status
                appointment.scheduleEpoc = input.appointmentEpoc
                appointment.desc = timelineItem.desc
                appointment.creatorID = creator
                appointment.rate = input.rate
                
                if let json = input.scheduler?.toJson() {
                    if var meta = appointment.meta {
                        meta.schedulerAppointment = json
                        appointment.meta = meta
                    } else {
                        appointment.meta = .init(data: [:], schedulerAppointment: json)
                    }
                }
                return appointment.update(on: req.db)
            }
        } else {
            let network = item.network
            let meta: MetaData = .init(data: ["carepackage_id": carepackageId,
                                              "item": item.id?.uuidString ?? ""],
                                       schedulerAppointment: input.scheduler?.toJson())
            let aptInput = AppointmentInput(creatorID: creator,
                                            title: network?.name ?? "appointment",
                                            status: timelineItem.status,
                                            kind: "appointment",
                                            desc: timelineItem.desc,
                                            scheduleEpoc: input.appointmentEpoc,
                                            duration: "60",
                                            memberID: member,
                                            orgID: org,
                                            meta: meta,
                                            memberBooking: network?.memberBook ?? false,
                                            networkId: network?.id,
                                            rate: input.rate)
            
            return try! AppointmentsController.createAppointment(req: req, input: aptInput).flatMap({ appointment in
                return try! self.attachAptsTo(req: req, item: item, appointments: [appointment])
            })
        }
    }
    
    fileprivate func attachAptsTo(req:Request, item: CarePackageItem, appointments: [Appointment]) throws -> EventLoopFuture<Void> {
        if appointments.isEmpty {
            return item.$appointments.detachAll(on: req.db)
        } else {
            return item.$appointments.detachAll(on: req.db).flatMap { _ in
                return item.$appointments.attach(appointments, on: req.db)
            }
        }
    }    
    
    func findCarePackage(req: Request, id:String) throws -> EventLoopFuture<CarePackage> {
        guard let uuid = UUID(uuidString: id) else { throw NetworkError.error(type: .badRequest, msg: "invalid id") }
        return CarePackage.query(on: req.db).filter(\.$id == uuid).with(\.$items).with(\.$org).first().unwrap(or: NetworkError.error(type: .carepackage))
    }
    
    func find(req: Request) throws -> EventLoopFuture<CarePackageItem> {
        return CarePackageItem.find(req.parameters.get("itemID"), on: req.db).unwrap(or: NetworkError.error(type: .carepackageItem))
    }
    
    fileprivate func addCreatorIfNeeded(req:Request, input:CarePackageInput, package:CarePackage) throws -> EventLoopFuture<CarePackage> {
        if let creatorID = input.creator {
            return try! UsersController.find(req: req, id: creatorID).flatMap { creator in
                package.creator = creatorID
                return package.update(on: req.db).transform(to: package)
            }
        } else {
            return req.eventLoop.future(package)
        }
    }
    
    fileprivate func addReciverIfNeeded(req:Request, input:CarePackageInput, package:CarePackage) throws -> EventLoopFuture<CarePackage> {
        if let recieverID = input.reciever {
            return try! MembersController.find(req: req, id: recieverID).flatMap { creator in
                package.reciever = recieverID
                return package.update(on: req.db).transform(to: package)
            }
        } else {
            return req.eventLoop.future(package)
        }
    }
    
    fileprivate func clean(services:[Service], req:Request) throws -> EventLoopFuture<[Service]> {
        return services.map {  service in
            let item = service
            item.id = nil
            return req.eventLoop.future(item).flatMap { ser in
                return try! self.clean(rules: ser.rules, req: req).transform(to: ser)
            }
        }.flatten(on: req.eventLoop)
    }
    
    fileprivate func clean(rules:[ServiceRule], req:Request) throws -> EventLoopFuture<[ServiceRule]> {
        return rules.map {  rule in
            let item = rule
            item.id = nil
            return req.eventLoop.future(item)
        }.flatten(on: req.eventLoop)
    }
    
    
    func update(req: Request) throws -> EventLoopFuture<CarePackage> {
        guard let id = req.parameters.get("cpID") else { throw NetworkError.error(type: .carepackage) }
        let input = try req.content.decode(CarePackageInput.self)
        return try CarePackagesController.find(req: req, id: id).flatMap { package in
            let package = input.returnUpdatedModel(package: package)
            return package.update(on: req.db).transform(to: package)
            //                .flatMap { service in
            //                try! self.updateRulesIfNeeded(req: req, input: input, service: service)
            //            }
        }
    }
    
    //MARK: - Delete
    func delete(req: Request) throws -> EventLoopFuture<SuccessResposne> {
        guard let id = req.parameters.get("cpID") else { throw NetworkError.error(type: .carepackage) }
        return try CarePackagesController.find(req: req, id: id).flatMap { package in
            return package.delete(on: req.db).transform(to: SuccessResposne(res: "complete"))
        }
    }
    
    func deleteNetworksItems(req: Request) throws -> EventLoopFuture<SuccessResposne> {
        guard let id = req.parameters.get("cpID") else { throw NetworkError.error(type: .carepackage) }
        guard let uuid = UUID(uuidString: id) else { throw NetworkError.error(type: .carepackage) }
        let input = try req.content.decode(DeleteNetworksInput.self)
        let networkIds = input.networks.compactMap({UUID(uuidString: $0)})
        return CarePackageItem.query(on: req.db)
            .filter(\.$carepackage.$id == uuid)
            .filter(\.$network.$id ~~ networkIds)
            .with(\.$appointments)
            .with(\.$network)
            .all().flatMap { items in
                return items.delete(on: req.db).transform(to: SuccessResposne(res: "complete"))
            }
        
    }
    
    func deleteItem(req: Request) throws -> EventLoopFuture<CarePackageItem> {
        guard let id = req.parameters.get("cpID") else { throw NetworkError.error(type: .carepackage) }
        guard let itemID = req.parameters.get("itemID") else { throw NetworkError.error(type: .carepackage) }
        let token = try extractBearerToken(from: req)
        return try! lookup(token: token, req: req).flatMap { tokenModel in
            if let uuid = tokenModel.user.id {
                return try! UsersController.findAuth(req: req, auth: uuid.uuidString).flatMap { user in
                    return try! CarePackagesController.findCarePackageItem(req: req, id: itemID).flatMap { package in
                        
                        return package.delete(on: req.db).transform(to: package).flatMap { itwm in
                            if let carepackage = package.carepackage, let network = package.network {
                                let timelineItem = TimeLineItemMessage.carepackage(status: "removed",
                                                                                   package: carepackage,
                                                                                   user: user,
                                                                                   network: network).toTimelineItem()
                                return timelineItem.create(on: req.db).transform(to: timelineItem).flatMap { timeline in
                                    timeline.$creator.id = user.id
                                    return timeline.update(on: req.db).transform(to: package)
                                }
                            } else {
                                let removedBy = TimelineItem(carepackageID: id,
                                             status: "removed",
                                             desc: "\(package.network?.name ?? "Network") was removed.",
                                             title: nil,
                                             memberId: nil,
                                             visible: false,
                                             meta: nil)
                                return removedBy.create(on: req.db).transform(to: removedBy).flatMap { timeline in
                                    timeline.$creator.id = user.id
                                    return timeline.update(on: req.db).transform(to: package)
                                }
                            }
                        }
                    }
                }
            } else {
                return req.eventLoop.makeFailedFuture(NetworkError.error(type: .carepackage))
            }
        }
    }
    
    func findTimeline(req: Request, uuid:UUID?) throws -> EventLoopFuture<TimelineItem> {
        return TimelineItem.find(uuid, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .timeline) }
            return foundModel
        }
    }
    
    func lookup(token:String, req: Request) throws -> EventLoopFuture<Token> {
        return Token.query(on: req.db).filter(\.$value == token).with(\.$user).first().flatMapThrowing { model in
            guard let foundModel = model else {  throw NetworkError.error(type: .token) }
            return foundModel
        }
    }
    
    //    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<CarePackage>, req:Request, org:String) -> QueryBuilder<CarePackage> {
        let title:String?           = req.query["title"]
        let type:String?            = req.query["type"]
        let creator:String?         = req.query["creator"]
        let reciever:String?        = req.query["reciever"]
        let status:String?          = req.query["status"]
        //        let lastName:String?             = req.query["lastName"]
        //        let email:String?                = req.query["email"]
        //
        //        if let nm = firstName  {
        //            query.filter(\.$firstName, .custom("ilike"), "%\(nm.lowercased())%")
        //        }
        //
        //        if let nm = lastName  {
        //            query.filter(\.$lastName, .custom("ilike"), "%\(nm.lowercased())%")
        //        }
        //
        if let typ = type {
            query.filter(\.$type == typ)
        }
        
        if let sts = status {
            if sts.lowercased() == "all" {
                query.filter(\.$status ~~ ["active", "draft"])
            } else {
                query.filter(\.$status == sts.lowercased())
            }
            
        }
        
        if let tl = title {
            query.filter(\.$title, .custom("ilike"), "%\(tl)%")
        }
        
        if let cr = creator?.lowercased() {
            query.filter(\.$creator, .custom("ilike"), "%\(cr)%")
        }
        
        if let rec = reciever?.lowercased() {
            query.filter(\.$reciever, .custom("ilike"), "%\(rec)%")
        }
        
        //        return query.with(\.$attachments).with(\.$teams)
        return query
            .with(\.$org)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(uuidString: org)!)
            .with(\.$items, { item in
                item.with(\.$network, { network in
                    network
                        .with(\.$address)
                        .with(\.$services)
                })
            })
            .with(\.$reason)
            .sort(\.$updatedAt, .descending)
    }
}



func extractBearerToken(from request: Request) throws -> String {
    guard let authorizationHeader = request.headers.first(name: "Authorization") else {
        throw Abort(.unauthorized, reason: "Authorization Error")
    }

    guard authorizationHeader.hasPrefix("Bearer ") else {
        throw Abort(.unauthorized, reason: "Authorization Error")
    }

    return String(authorizationHeader.dropFirst("Bearer ".count))
}
