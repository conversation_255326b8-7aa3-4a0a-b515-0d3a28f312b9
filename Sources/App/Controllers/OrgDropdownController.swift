//
//  OrgDropdownController.swift
//  
//
//  Created by <PERSON> on 7/28/25.
//

import Foundation
import Vapor
import Fluent

struct OrgDropdownController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        // Apply admin authentication middleware to all org dropdown routes
        let orgRoutes = routes.grouped("org", ":orgID").grouped(AdminAuthMiddleware())
        
        // Main dashboard route
        orgRoutes.get(use: dashboard)
        
        // Create item routes
        orgRoutes.get("constants", ":section", "create", use: showCreateForm)
        orgRoutes.post("constants", ":section", "create", use: createItem)
        
        // Remove all items route
        orgRoutes.post("constants", ":section", "remove-all", use: removeAllItems)
        
        // Individual item operations (using item key instead of ID)
        orgRoutes.delete("constants", ":section", ":itemID", use: deleteItem)
        orgRoutes.put("constants", ":section", ":itemID", use: updateItem)

        // De<PERSON>ult constants endpoints
        orgRoutes.get("constants", "defaults", use: getDefaultConstants)
        orgRoutes.post("constants", ":section", "import-defaults", use: importDefaults)
    }
    
    // MARK: - Dashboard
    func dashboard(req: Request) throws -> EventLoopFuture<View> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString) else {
            throw Abort(.badRequest, reason: "Invalid organization ID")
        }

        // Admin authentication is handled by AdminAuthMiddleware
        // Allow admin to access any organization for management
        
        // Fetch organization with meta data
        return Organization.find(orgID, on: req.db)
            .flatMapThrowing { org in
                guard let org = org else {
                    throw Abort(.notFound, reason: "Organization not found")
                }

                let constants = org.meta?.constants ?? [:]
                let defaults = ApplicationConstants()

                // Create context for the template
                let context = DashboardContext(
                    orgID: orgIDString,
                    sections: DropdownSection.allCases.map { section in
                        let sectionItems = constants[section.rawValue] ?? []
                        let defaultItems = self.getDefaultItemsForSection(section: section, from: defaults)
                        return SectionData(
                            key: section.rawValue,
                            displayName: section.displayName,
                            supportsColor: section.supportsColor,
                            items: sectionItems,
                            defaultItems: defaultItems,
                            isEmpty: sectionItems.isEmpty,
                            hasDefaults: !defaultItems.isEmpty
                        )
                    }
                )

                return context
            }
            .flatMap { context in
                return req.view.render("org-dropdown-dashboard", context)
            }
    }
    
    // MARK: - Create Item
    func showCreateForm(req: Request) throws -> EventLoopFuture<View> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let section = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        // Admin authentication is handled by AdminAuthMiddleware
        let context = CreateItemContext(
            orgID: orgIDString,
            section: section.rawValue,
            sectionDisplayName: section.displayName,
            supportsColor: section.supportsColor
        )
        
        return req.view.render("org-dropdown-create", context)
    }
    
    func createItem(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let _ = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        let input = try req.content.decode(CreateDropdownItemInput.self)

        let newItem = PickerItem(
            title: input.title,
            key: input.generateKey(),
            color: input.color
        )

        return Organization.find(orgID, on: req.db)
            .flatMapThrowing { org in
                guard let org = org else {
                    throw Abort(.notFound, reason: "Organization not found")
                }

                // Initialize meta if it doesn't exist
                if org.meta == nil {
                    org.meta = OrgMetaData(constants: [:], features: [], billing: nil)
                }

                // Add the new item to the section
                var constants = org.meta?.constants ?? [:]
                var sectionItems = constants[sectionString] ?? []
                sectionItems.append(newItem)
                constants[sectionString] = sectionItems
                org.meta?.constants = constants

                return org
            }
            .flatMap { org in
                return org.save(on: req.db).transform(to: req.redirect(to: "/org/\(orgIDString)"))
            }
    }
    
    // MARK: - Remove All Items
    func removeAllItems(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let _ = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        return Organization.find(orgID, on: req.db)
            .flatMapThrowing { org in
                guard let org = org else {
                    throw Abort(.notFound, reason: "Organization not found")
                }

                // Remove all items from the section
                var constants = org.meta?.constants ?? [:]
                constants[sectionString] = []
                org.meta?.constants = constants

                return org
            }
            .flatMap { org in
                return org.save(on: req.db).transform(to: req.redirect(to: "/org/\(orgIDString)"))
            }
    }
    
    // MARK: - Delete Individual Item
    func deleteItem(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let itemKey = req.parameters.get("itemID") else {
            req.logger.error("Delete item - Invalid parameters: orgID=\(req.parameters.get("orgID") ?? "nil"), section=\(req.parameters.get("section") ?? "nil"), itemID=\(req.parameters.get("itemID") ?? "nil")")
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        req.logger.info("Delete item - orgID: \(orgIDString), section: \(sectionString), itemKey: \(itemKey)")

        return Organization.find(orgID, on: req.db)
            .flatMapThrowing { org in
                guard let org = org else {
                    req.logger.error("Delete item - Organization not found: \(orgIDString)")
                    throw Abort(.notFound, reason: "Organization not found")
                }

                req.logger.info("Delete item - Found org: \(org.title)")

                // Remove the item with the specified key from the section
                var constants = org.meta?.constants ?? [:]
                var sectionItems = constants[sectionString] ?? []
                let originalCount = sectionItems.count
                sectionItems.removeAll { $0.key == itemKey }
                constants[sectionString] = sectionItems
                org.meta?.constants = constants

                req.logger.info("Delete item - Removed \(originalCount - sectionItems.count) items from section \(sectionString)")

                return org
            }
            .flatMap { org in
                return org.save(on: req.db).transform(to: HTTPStatus.ok)
            }
    }
    
    // MARK: - Update Item
    func updateItem(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let itemKey = req.parameters.get("itemID") else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        let input = try req.content.decode(UpdateDropdownItemInput.self)

        return Organization.find(orgID, on: req.db)
            .flatMapThrowing { org in
                guard let org = org else {
                    throw Abort(.notFound, reason: "Organization not found")
                }

                // Find and update the item with the specified key
                var constants = org.meta?.constants ?? [:]
                var sectionItems = constants[sectionString] ?? []

                if let index = sectionItems.firstIndex(where: { $0.key == itemKey }) {
                    if let title = input.title {
                        sectionItems[index].title = title
                    }
                    if let color = input.color {
                        sectionItems[index].color = color
                    }
                    if let key = input.key {
                        sectionItems[index].key = key
                    }
                    constants[sectionString] = sectionItems
                    org.meta?.constants = constants
                }

                return org
            }
            .flatMap { org in
                return org.save(on: req.db).transform(to: req.redirect(to: "/org/\(orgIDString)"))
            }
    }

    // MARK: - Default Constants
    func getDefaultConstants(req: Request) throws -> ApplicationConstants {
        return ApplicationConstants()
    }

    func importDefaults(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let section = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        let input = try req.content.decode(ImportDefaultsInput.self)
        let defaults = ApplicationConstants()

        return Organization.find(orgID, on: req.db)
            .flatMapThrowing { org in
                guard let org = org else {
                    throw Abort(.notFound, reason: "Organization not found")
                }

                // Get default items for this section
                let defaultItems = self.getDefaultItemsForSection(section: section, from: defaults)

                // Filter to only selected items
                let selectedItems = defaultItems.filter { item in
                    input.selectedKeys.contains(item.key)
                }

                // Initialize meta if it doesn't exist
                if org.meta == nil {
                    org.meta = OrgMetaData(constants: [:], features: [], billing: nil)
                }

                // Add selected items to the organization's constants
                var constants = org.meta?.constants ?? [:]
                var sectionItems = constants[sectionString] ?? []

                // Avoid duplicates by checking existing keys
                for item in selectedItems {
                    if !sectionItems.contains(where: { $0.key == item.key }) {
                        sectionItems.append(item)
                    }
                }

                constants[sectionString] = sectionItems
                org.meta?.constants = constants

                return org
            }
            .flatMap { org in
                return org.save(on: req.db).transform(to: HTTPStatus.ok)
            }
    }

    // Helper method to get default items for a specific section
    private func getDefaultItemsForSection(section: DropdownSection, from defaults: ApplicationConstants) -> [PickerItem] {
        switch section {
        case .roles:
            return [] // No platform defaults for roles
        case .noteTags:
            return defaults.noteTags
        case .taskTypes:
            return defaults.taskTypes
        case .memberTags:
            return defaults.memberTags
        case .carePackageSections:
            return defaults.carePackageSections
        case .taskCompletionReasons:
            return defaults.taskCompletionReasons
        case .followUpOutcomes:
            return defaults.followUpOutcomes
        case .assessmentDueOptions:
            return defaults.assessmentDueOptions
        case .reviewFrequencies:
            return defaults.reviewFrequencies
        case .programStatuses:
            return defaults.programStatuses
        case .programTypes:
            return defaults.programTypes
        case .attachmentTypes:
            return defaults.attachmentTypes
        case .uploadKinds:
            return defaults.uploadTypes.map { PickerItem(title: $0, key: $0) }
        case .networkStatus:
            return defaults.networkStatus
        case .planCoverage:
            return defaults.planCoverage
        case .contactTaskTypes:
            return defaults.contactTaskTypes
        case .memberTypes:
            return defaults.memberTypes
        case .memberStatus:
            return defaults.staffStatus // Using staffStatus as memberStatus equivalent
        case .relationshipTypes:
            return defaults.relationship
        case .problemTypes:
            return defaults.problemTypes
        case .interventionTypes:
            return defaults.interventionTypes
        case .goalTypes:
            return defaults.goalTypes
        case .notesSubcategory:
            return defaults.notesSubcategory
        case .reasons:
            return defaults.reasons
        case .staffStatus:
            return defaults.staffStatus
        }
    }
}

// MARK: - Context Structs
struct DashboardContext: Content {
    let orgID: String
    let sections: [SectionData]
}

struct SectionData: Content {
    let key: String
    let displayName: String
    let supportsColor: Bool
    let items: [PickerItem]
    let defaultItems: [PickerItem]
    let isEmpty: Bool
    let hasDefaults: Bool
}

struct CreateItemContext: Content {
    let orgID: String
    let section: String
    let sectionDisplayName: String
    let supportsColor: Bool
    let isTaskTypes: Bool
    let taskTypeOptions: [String]?
    let isRoles: Bool
    let roleTypeOptions: [String]?

    init(orgID: String, section: String, sectionDisplayName: String, supportsColor: Bool) {
        self.orgID = orgID
        self.section = section
        self.sectionDisplayName = sectionDisplayName
        self.supportsColor = supportsColor
        self.isTaskTypes = section == "taskTypes"
        self.taskTypeOptions = section == "taskTypes" ? ["assessment", "contact", "visit", "custom"] : nil
        self.isRoles = section == "roles"
        self.roleTypeOptions = section == "roles" ? ["admin", "member", "staff"] : nil
    }
}

// MARK: - Input Structs
struct CreateDropdownItemInput: Content {
    let title: String
    let color: String?
    let key: String?
    let taskType: String?
    let roleType: String?

    func generateKey() -> String {
        if let providedKey = key, !providedKey.isEmpty {
            return providedKey
        }

        let baseKey = title.lowercased()
            .replacingOccurrences(of: " ", with: "_")
            .replacingOccurrences(of: "[^a-z0-9_]", with: "", options: .regularExpression)

        // Special handling for task types
        if let taskType = taskType, !taskType.isEmpty && taskType != "custom" {
            return "\(baseKey)_\(taskType)"
        }

        // Special handling for role types
        if let roleType = roleType, !roleType.isEmpty {
            return "\(baseKey)_\(roleType)"
        }

        return baseKey
    }
}

struct UpdateDropdownItemInput: Content {
    let title: String?
    let color: String?
    let key: String?
}

struct ImportDefaultsInput: Content {
    let selectedKeys: [String]
}

// MARK: - Supported Sections
enum DropdownSection: String, CaseIterable {
    // Original sections
    case roles = "roles"
    case noteTags = "noteTags"
    case taskTypes = "taskTypes"
    case memberTags = "memberTags"
    case carePackageSections = "carePackageSections"
    case taskCompletionReasons = "taskCompletionReasons"

    // Assessment/Program Related
    case followUpOutcomes = "followUpOutcomes"
    case assessmentDueOptions = "assessmentDueOptions"
    case reviewFrequencies = "reviewFrequencies"
    case programStatuses = "programStatuses"
    case programTypes = "programTypes"

    // System/Admin
    case attachmentTypes = "attachmentTypes"
    case uploadKinds = "uploadKinds"
    case networkStatus = "networkStatus"
    case planCoverage = "planCoverage"

    // Contact Task Types
    case contactTaskTypes = "contactTaskTypes"

    // Member/User Related
    case memberTypes = "memberTypes"
    case memberStatus = "memberStatus"
    case relationshipTypes = "relationshipTypes"

    // Additional Application Constants
    case problemTypes = "problemTypes"
    case interventionTypes = "interventionTypes"
    case goalTypes = "goalTypes"
    case notesSubcategory = "notesSubcategory"
    case reasons = "reasons"
    case staffStatus = "staffStatus"

    var displayName: String {
        switch self {
        // Original sections
        case .roles:
            return "Roles"
        case .noteTags:
            return "Note Tags"
        case .taskTypes:
            return "Task Types"
        case .memberTags:
            return "Member Tags"
        case .carePackageSections:
            return "Care Package Sections"
        case .taskCompletionReasons:
            return "Task Completion Reasons"

        // Assessment/Program Related
        case .followUpOutcomes:
            return "Follow-Up Outcomes"
        case .assessmentDueOptions:
            return "Assessment Due Options"
        case .reviewFrequencies:
            return "Review Frequencies"
        case .programStatuses:
            return "Program Statuses"
        case .programTypes:
            return "Program Types"

        // System/Admin
        case .attachmentTypes:
            return "Attachment Types"
        case .uploadKinds:
            return "Upload Kinds"
        case .networkStatus:
            return "Network Status"
        case .planCoverage:
            return "Plan Coverage"

        // Contact Task Types
        case .contactTaskTypes:
            return "Contact Task Types"

        // Member/User Related
        case .memberTypes:
            return "Member Types"
        case .memberStatus:
            return "Member Status"
        case .relationshipTypes:
            return "Relationship Types"

        // Additional Application Constants
        case .problemTypes:
            return "Problem Types"
        case .interventionTypes:
            return "Intervention Types"
        case .goalTypes:
            return "Goal Types"
        case .notesSubcategory:
            return "Notes Subcategory"
        case .reasons:
            return "Reasons"
        case .staffStatus:
            return "Staff Status"
        }
    }

    var supportsColor: Bool {
        switch self {
        case .noteTags, .memberTags, .programStatuses, .memberStatus, .staffStatus, .problemTypes, .goalTypes:
            return true
        default:
            return false
        }
    }
}
