//
//  File.swift
//  
//
//  Created by <PERSON> on 1/31/23.
//

import Foundation
import Vapor
import Fluent


struct HouseholdsController: RouteCollection {
    
    var houseID:String = "houseID"
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("households")
        
        users.get(use: index)
        users.get("page", use: indexPage)
        users.get([":\(houseID)", "members"], use: members)
        users.get([":\(houseID)", "careteam"], use: careteam)
        users.get([":\(houseID)", "tags"], use: getTags)
        users.get(":\(houseID)", use: lookup)
        
        users.post(use: create)
        users.post([":\(houseID)", "attachments"], use: creatAttachment)
        users.post([":\(houseID)", "tags"], use: createTags)
        
        users.put(":\(houseID)", use: update)
        users.put([":\(houseID)", "members"], use: attachMember)
        users.put([":\(houseID)", "pets"], use: createPet)
        users.put([":\(houseID)", "teams"], use: assignTeam)
        users.put([":\(houseID)", "tags", ":tagId"], use: updateTag)
        
        
        users.delete([":\(houseID)", "members", ":memeberID"], use: removeMember)
        users.delete([":\(houseID)", "tags", ":tagId"], use: deleteTag)
        users.delete([":\(houseID)", "headofhouse"], use: removeHeadOfHouse)
        users.delete([":\(houseID)", "teams"], use: removeTeams)
        users.delete(":\(houseID)", use: deleteHousehold)
    }
    
    
    //MARK: - Fetches
    func lookup(req: Request) throws -> EventLoopFuture<Household> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .household)}
        return self.buildQuery(query: Household.query(on: req.db), req: req, orgID: nil).filter(\.$id == networkID).first().flatMapThrowing { model in
            guard let foundModel = model else { throw NetworkError.error(type: .household) }
            return foundModel
        }
    }
    
    func members(req: Request) throws -> EventLoopFuture<Page<Member>> {
        guard let id = req.parameters.get("org") else { throw Abort(.notFound, reason: "Org ID is required") }
        return try! MembersController().orgMembers(req: req, orgID: id)
    }        
    
    static func find(req: Request, id:String) throws -> EventLoopFuture<Household> {
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .household)}
        return Household.find(networkID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .household) }
            return foundModel
        }
    }
    
    func careteam(req: Request) throws -> EventLoopFuture<Household> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .household)}
        return Household.query(on: req.db)
            .filter(\.$id == networkID)
            .with(\.$address)
            .with(\.$teams, { team in
                team.with(\.$navigators, { nav in
                    nav.with(\.$attachments)
                    nav.with(\.$teams)
                })
            })
            .first()
            .flatMapThrowing { model in
            guard let house = model else { throw NetworkError.error(type: .household) }
            return house
        }
    }
    static func query(req: Request, id:String) throws -> EventLoopFuture<Household> {
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .household)}
        return Household.query(on: req.db)
            .filter(\.$id == networkID)
            .with(\.$address)
            .first()
            .flatMapThrowing { model in
            guard let house = model else { throw NetworkError.error(type: .household) }
            return house
        }
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Household]> {
        let org:String?            = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return self.buildQuery(query: Household.query(on: req.db), req: req, orgID: orgID)
            .sort(\.$updatedAt, .ascending)
            .all()
    }
    
    func indexPage(req: Request) throws -> EventLoopFuture<Page<Household>> {
        let org:String?            = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return self.buildQuery(query: Household.query(on: req.db), req: req, orgID: orgID)
            .sort(\.$updatedAt, .ascending)
            .paginate(for: req)
    }

    
    //MARK: - Create
    static func createWith(req: Request, authUser:AuthUser, signup:UserSignup) throws -> EventLoopFuture<Void> {
        guard let authUser = try? authUser.asPublic() else {
            return req.eventLoop.future(error: Abort(.internalServerError))
        }
        return signup.user(auth: authUser.id.uuidString).create(on: req.db)
    }
    
    func create(req: Request) throws -> EventLoopFuture<Household> {
        let input = try req.content.decode(HouseholdInput.self)
        guard let orgID = input.orgID else { throw NetworkError.error(type: .organization) }
        let house = input.minHousehold()
        return try! OrgsController.find(req: req, id: orgID).flatMap { org in
            return org.$households.create(house, on: req.db).transform(to: house).flatMap { household in
                let household = input.returnUpdatedModel(household: household)
                return try! updateHouseholdIfNeeded(req: req, input: input, household: household)
            }
        }
    }
    
    func creatAttachment(req: Request) throws -> EventLoopFuture<Household> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        let input = try req.content.decode(UploadInput.self)
        
        return try! HouseholdsController.find(req: req, id: id).flatMap { house in
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let attach = Attachment(name: input.name, 
                                        kind: input.type,
                                        type: "image",
                                        url: resposne.secure_url,
                                        category: input.category,
                                        refID: resposne.public_id)
                return house.$attachments.create(attach, on: req.db).transform(to: house)
            }
        }
    }
    
    fileprivate func findAnimal(id:String, req: Request) throws -> EventLoopFuture<Animal> {
        return Animal.find(UUID(uuidString: id), on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .pet) }
            return foundModel
        }
    }
    
    func createPet(req: Request) throws -> EventLoopFuture<Animal> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        let input = try req.content.decode(HouseholdCreatePetInput.self)
        return try! HouseholdsController.find(req: req, id: id).flatMap { house in
            return try! findAnimal(id: input.pet, req: req).flatMap { foundModel in
                foundModel.$household.id = house.id
                return foundModel.update(on: req.db).transform(to: foundModel).flatMap { ani in
                    return req.eventLoop.future(ani)
                }
            }
        }
    }
    
    
    
    //MARK: - Update
    func attachMember(req: Request) throws -> EventLoopFuture<Household> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        let input = try req.content.decode(HouseholdAttachMembersInput.self)
        print(input.members)
        guard !input.members.isEmpty else { throw NetworkError.error(type: .badRequest, msg: "members required") }
        return try HouseholdsController.find(req: req, id: id).flatMap { house in
            return try! MembersController.findMembers(req: req, ids: input.members).flatMap { members in
                return house.$members.attach(members, on: req.db).transform(to: house)
            }
        }
    }
    
    func update(req: Request) throws -> EventLoopFuture<Household> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        let input = try req.content.decode(HouseholdInput.self)
        return try HouseholdsController.query(req: req, id: id).flatMap { house in
            let household = input.returnUpdatedModel(household: house)
            return try! updateHouseholdIfNeeded(req: req, input: input, household: household)
        }
    }
    
    func updateHouseholdIfNeeded(req:Request, input:HouseholdInput, household:Household) throws -> EventLoopFuture<Household> {
        return try! updateHeadOfHouseIfNeeded(req: req, input: input, household: household).flatMap { household in

            return try! updateAddressIfNeeded(req: req, input: input, house: household).flatMap { upHousehold in
                
                return try! attachMembersIfNeeded(req: req, input: input, household: household).flatMap { upHousehold in
                    
                    return try! attachTeamsIfNeeded(req: req, input: input, household: household).flatMap { upHousehold in
                        
                        return try! updateAttachmentIfNeeded(req: req, input: input, household: household)
                    }
                }
            }
        }
    }
    
    func removeTeams(req:Request) throws -> EventLoopFuture<Household> {
        return try self.assignOrRemoveTeams(req: req, remove: true)
    }
    
    func deleteHousehold(req:Request) throws -> EventLoopFuture<Household> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        return try! HouseholdsController.find(req: req, id: id).flatMap { house in
            return house.delete(on: req.db).transform(to: house)
        }
    }
    
    func assignTeam(req:Request) throws -> EventLoopFuture<Household> {
        return try self.assignOrRemoveTeams(req: req, remove: false)
    }
    
    fileprivate func assignOrRemoveTeams(req:Request, remove:Bool) throws -> EventLoopFuture<Household> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        let input = try req.content.decode(HouseholdInput.self)
        guard let teamIds =  input.teams, !teamIds.isEmpty  else { throw NetworkError.error(type: .team) }
        return try! HouseholdsController.find(req: req, id: id).flatMap { house in
            return try! TeamsController.findAll(req: req, ids: teamIds).flatMap { teams in
                if remove {
                    return house.$teams.detach(teams, on: req.db).transform(to: house)
                } else {
                    return house.$teams.attach(teams, on: req.db).transform(to: house)
                }
            }
         }
    }
    
    fileprivate func attachTeamsIfNeeded(req: Request, input:HouseholdInput, household:Household) throws -> EventLoopFuture<Household> {
        guard let teamIds =  input.teams else {  return req.eventLoop.future(household) }
        if teamIds.isEmpty {
            return household.$teams.detachAll(on: req.db).transform(to: household)
        } else {
            return try! TeamsController.findAll(req: req, ids: teamIds).flatMap { teams in
                return household.$teams.detachAll(on: req.db).flatMap { _ in
                    return household.$teams.attach(teams, on: req.db).transform(to: household)
                }
            }
        }
    }
    
    fileprivate func attachMembersIfNeeded(req: Request, input:HouseholdInput, household:Household) throws -> EventLoopFuture<Household> {
        if let members = input.members {
            if members.isEmpty {
                return household.$members.detachAll(on: req.db).transform(to: household)
            } else {
                return try! MembersController.findMembers(req: req, ids: members).flatMap { members in
                    return household.$members.detachAll(on: req.db).flatMap { _ in
                        return household.$members.attach(members, on: req.db).transform(to: household)
                    }
                }
            }
        } else {
            return req.eventLoop.future(household)
        }
    }
    
    fileprivate func updateAttachmentIfNeeded(req: Request, input:HouseholdInput, household:Household) throws -> EventLoopFuture<Household> {
        if let attachInput = input.attachment {
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let attach = Attachment(name: attachInput.name, 
                                        kind: attachInput.type,
                                        type: "image",
                                        url: resposne.secure_url,
                                        category: attachInput.category,
                                        refID: resposne.public_id)
                return household.$attachments.create(attach, on: req.db).transform(to: household)
            }
        } else {
            return req.eventLoop.future(household)
        }
    }
    
    fileprivate func updateAddressIfNeeded(req:Request, input:HouseholdInput, house:Household) throws -> EventLoopFuture<Household> {
        return try AddressService.updateAddressIfNeeded(req: req, input: input.address, model: house).transform(to: house)
    }
    
    fileprivate func updateHeadOfHouseIfNeeded(req: Request, input: HouseholdInput, household:Household)  throws -> EventLoopFuture<Household> {
        if let memberID = input.headOfHouseID {
            household.$headOfHouse.id = UUID(memberID)
            return household.update(on: req.db).transform(to: household)
        } else {
            return household.update(on: req.db).transform(to: household)
        }
    }
    
    //MARK: - Delete
    
    func removeMember(req:Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        guard let memeberId = req.parameters.get("memeberID") else { throw NetworkError.error(type: .member) }
        return try HouseholdsController.find(req: req, id: id).flatMap { house in
            return try! MembersController.find(req: req, id: memeberId).flatMap { member in
                return house.$members.detach(member, on: req.db).transform(to: HTTPStatus.accepted)
            }
        }
    }
    
    func removeHeadOfHouse(req:Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        return try HouseholdsController.find(req: req, id: id).flatMap { house in
            house.$headOfHouse.id = nil
            return house.update(on: req.db).transform(to: HTTPStatus.accepted)
        }
    }
    
    
    func removeHeadOfHouseId(req:Request, id: String) throws -> EventLoopFuture<HTTPStatus> {
        return try HouseholdsController.find(req: req, id: id).flatMap { house in
            house.$headOfHouse.id = nil
            return house.update(on: req.db).transform(to: HTTPStatus.accepted)
        }
    }
//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<Household>, req:Request, orgID:String?) -> QueryBuilder<Household> {
        let title:String?            = req.query["title"]
        let type:String?            = req.query["type"]
        let search:String?            = req.query["search"]
        var query = query
        if let typ = type {
            query.filter(\.$type == typ)
        }
        
        if let tl = title {
            query.filter(\.$title, .custom("ilike"), "%\(tl.lowercased())%")            
        }
        
        if let search {
            query.filter(\.$title, .custom("ilike"), "%\(search.lowercased())%")
        }
        
        query = query.with(\.$headOfHouse)
            .with(\.$attachments)
            .with(\.$address)
            .with(\.$members, { member in
                member.with(\.$attachments)
                member.with(\.$address)
                member.with(\.$schools)
                member.with(\.$phones)
            })
            .with(\.$teams)
            .with(\.$pets, { pet in
                pet.with(\.$attachments)
            })
        
        if let org = orgID {
            query.join(parent: \.$org)
                .filter(Organization.self, \.$id == UUID(uuidString: org)!)
        }
//        return query.with(\.$attachments).with(\.$teams)
        return query

    }

    //MARK: - Tag CRUD Operations

    func getTags(req: Request) throws -> EventLoopFuture<[Tag]> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        return try HouseholdsController.find(req: req, id: id).flatMap { household in
            return household.$tags.query(on: req.db)
                .sort(\.$createdAt, .descending)
                .all()
        }
    }

    func createTags(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        let input = try req.content.decode(HouseholdTagCreateInput.self)
        let allTags = input.tags.compactMap({ $0.tag() })

        guard !allTags.isEmpty else {
            throw NetworkError.error(type: .badRequest, msg: "At least one tag is required")
        }

        return try HouseholdsController.find(req: req, id: id).flatMap { household in
            return household.$tags.create(allTags, on: req.db).transform(to: .created)
        }
    }

    func updateTag(req: Request) throws -> EventLoopFuture<Tag> {
        guard let householdId = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        guard let tagId = req.parameters.get("tagId") else { throw NetworkError.error(type: .tag) }
        guard let tagUUID = UUID(tagId) else { throw NetworkError.error(type: .tag) }

        let input = try req.content.decode(HouseholdTagUpdateInput.self)

        return try HouseholdsController.find(req: req, id: householdId).flatMap { household in
            return Tag.query(on: req.db)
                .filter(\.$id == tagUUID)
                .filter(\.$household.$id == household.id!)
                .first()
                .unwrap(or: Abort(.notFound, reason: "Tag not found for this household"))
                .flatMap { tag in
                    let updatedTag = input.updateTag(tag)
                    return updatedTag.update(on: req.db).transform(to: updatedTag)
                }
        }
    }

    func deleteTag(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let householdId = req.parameters.get(houseID) else { throw NetworkError.error(type: .household) }
        guard let tagId = req.parameters.get("tagId") else { throw NetworkError.error(type: .tag) }
        guard let tagUUID = UUID(tagId) else { throw NetworkError.error(type: .tag) }

        return try HouseholdsController.find(req: req, id: householdId).flatMap { household in
            return Tag.query(on: req.db)
                .filter(\.$id == tagUUID)
                .filter(\.$household.$id == household.id!)
                .first()
                .unwrap(or: Abort(.notFound, reason: "Tag not found for this household"))
                .flatMap { tag in
                    return tag.delete(on: req.db).transform(to: .noContent)
                }
        }
    }
}

