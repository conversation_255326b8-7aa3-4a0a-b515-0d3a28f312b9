//
//  File.swift
//  
//
//  Created by <PERSON> on 1/30/23.
//

import Foundation
import Vapor
import Fluent


struct UserUpdateInput: Content {
    static var param = "userID"
//    var email:      String? ///need to update auth as well
    var firstName:  String?
    var middleName: String?
    var lastName:   String?
    var profile:    String? //url
    var color:      String?
    var phone:      String?
    var roles:     [String]?    
    var meta:       MetaData?
    var teams:     [String]?
        
    func returnUpdatedModel(user:User) -> User {
        if let fn = firstName{
            user.firstName = fn
        }
        if let mn = middleName{
            user.middleName = mn
        }
        if let ln = lastName{
            user.lastName = ln
        }
        if let pro = profile{
            user.profile = pro
        }
        if let clr = color{
            user.color = clr
        }
        if let rl = roles{
            user.roles = rl
        }
        if let meta = meta {
            user.meta = meta
        }
        
        return user
    }
}

struct UsersController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("users")
        users.get(use: index)
        users.get(":userID", use: lookup)
        users.get([":userID", "chats"], use: chats)
        users.get([":userID", "memberChats"], use: memberChats)
        users.get([":userID","verify"], use: verify)
        
        users.post(use: create)
        users.post([":userID", "attachments"], use: creatAttachment)
        users.put(":userID", use: update)
        users.delete(":userID", use: delete)
        
        
    }
    
    //MARK: - Delete
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .member) }
        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .actions)
                
        return try AuthController.userIdFromToken(req: req).flatMap { navId in
            return try! UsersController.find(req: req, id: id).flatMap { user in
                if let auth = user.auth, let uuid = UUID(uuidString: auth) {
                    return try! findAuth(req: req, uuid: uuid).flatMap{ authUser in
                        return try! findAuthTokens(req: req, authUser: authUser).flatMap { token in
                            return token.delete(on: req.db).flatMap { _ in
                                return authUser.delete(on: req.db).flatMap { _ in
                                    return user.delete(on: req.db).flatMap { _ in
                                        return cloudwatch.putLog(message: logDeleteMessage(navId: navId, userId: id), on: req.eventLoop).transform(to: .accepted)
                                    }
                                }
                            }
                        }
                    }
                } else {
                    return user.delete(on: req.db).transform(to: .accepted)
                }
            }
        }
    }
    
    fileprivate func findAuthTokens(req:Request, authUser:AuthUser) throws -> EventLoopFuture<[Token]> {
        return try! Token.query(on: req.db).join(parent: \.$user).filter(AuthUser.self, \.$id == authUser.requireID()).all().flatMapThrowing() { tokens in
            return tokens
        }
    }
    
    fileprivate func findAuthToken(req:Request, authUser:AuthUser) throws -> EventLoopFuture<Token> {
        return try! Token.query(on: req.db).join(parent: \.$user).filter(AuthUser.self, \.$id == authUser.requireID()).first().flatMapThrowing() { token in
            guard let foundModel = token else {  throw NetworkError.error(type: .user) }
            return foundModel
        }
    }
    
    fileprivate func findAuth(req:Request, uuid:UUID) throws -> EventLoopFuture<AuthUser> {
        return AuthUser.query(on: req.db).filter(\.$id == uuid).first().flatMapThrowing() { authUser in
            guard let foundModel = authUser else {  throw NetworkError.error(type: .user) }
            return foundModel
        }
    }
    
    func verify(req: Request) throws -> EventLoopFuture<Device> {
        return try APNSPushController.verify(req: req)
    }
    
    func chats(req: Request) throws -> EventLoopFuture<Page<Chat>> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .user) }
        return try! ChatsController.creatorChats(req: req, creatorID: id)
    }
    
    func memberChats(req: Request) throws -> EventLoopFuture<Page<MemberChat>> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .user) }
        return try! MemberChatsController.chatsForMember(creatorID: id, req: req)
    }
    
    func lookup(req: Request) throws -> EventLoopFuture<User> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .user) }
        guard let networkID = UUID(id) else {  throw NetworkError.error(type: .user) }
        return self.buildQuery(query: User.query(on: req.db), req: req, org: nil).filter(\.$id == networkID).first().flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .user) }
            return foundModel
        }
    }
    
    static func createWith(req: Request, authUser:AuthUser, signup:UserSignup, org:Organization) throws -> EventLoopFuture<Void> {
        guard let authUser = try? authUser.asPublic() else {
            return req.eventLoop.future(error: Abort(.internalServerError))
        }
        var user = signup.user(auth: authUser.id.uuidString)
        user.meta = MetaData.defaultAccess
        return org.$users.create(user, on: req.db)
    }
    
    static func findAll(req: Request, ids:[String]) throws -> EventLoopFuture<[User]> {
        let allids = ids.compactMap({ UUID(uuidString: $0)})
        return User.query(on: req.db)
            .filter(\.$id ~~ allids)            
            .with(\.$attachments)
            .all()
    }
    
    static func find(req: Request, id:String) throws -> EventLoopFuture<User> {
        guard let networkID = UUID(id) else {  throw NetworkError.error(type: .user) }
        return User.find(networkID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else {  throw NetworkError.error(type: .user) }
            return foundModel
        }
    }
    
    static func find(req: Request, uuid:UUID) throws -> EventLoopFuture<User> {
        return User.find(uuid, on: req.db).flatMapThrowing() { model in
            guard let foundModel = model else {  throw NetworkError.error(type: .user) }
            return foundModel
        }
    }
    
    static func findAuth(req: Request, auth:String) throws -> EventLoopFuture<User> {
        return User.query(on: req.db)
            .filter(\.$auth == auth)
            .with(\.$org)
            .with(\.$teams)
            .with(\.$attachments).first().flatMapThrowing() { model in
            guard let foundModel = model else {  throw NetworkError.error(type: .user) }
            return foundModel
        }
    }
    
    func index(req: Request) throws -> EventLoopFuture<Page<User>> {
        let org:String?            = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return self.buildQuery(query: User.query(on: req.db), req: req, org: orgID).sort(\.$firstName).paginate(for: req)
    }

    func create(req: Request) throws -> EventLoopFuture<User> {
        let user = try req.content.decode(User.self)
        user.meta = MetaData.defaultAccess        
        return user.save(on: req.db).transform(to: user)
    }
    
    func update(req: Request) throws -> EventLoopFuture<User> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .user) }
        let input = try req.content.decode(UserUpdateInput.self)
        return try UsersController.find(req: req, id: id).flatMap { usr in
            let user = input.returnUpdatedModel(user: usr)
            return user.update(on: req.db).transform(to: user).flatMap { user in
                return try! createPhoneIfNeeded(req: req, phone: input.phone, user: user).flatMap({ updatedUser in
                    return req.eventLoop.future(updatedUser).flatMap { createdUser in
                        if let teamIds = input.teams {
                            return try! teams(req: req, teamIds: teamIds, user: user).flatMap({ user in
                                return req.eventLoop.future(user)
                            })
                        } else {
                            return req.eventLoop.future(createdUser)
                        }
                    }
                })
            }
        }
    }
    
    func teams(req: Request, teamIds:[String], user: User) throws -> EventLoopFuture<User> {
        return try! TeamsController.findAll(req: req, ids: teamIds).flatMap{ allTeams in
            return user.$teams.detachAll(on: req.db).flatMap { _ in
                return user.$teams.attach(allTeams, on: req.db).transform(to: user)
            }
        }
    }
    
    func creatAttachment(req: Request) throws -> EventLoopFuture<User> {
        guard let id = req.parameters.get("userID") else {  throw NetworkError.error(type: .user) }
        let input = try req.content.decode(UploadInput.self)
        return try! UsersController.find(req: req, id: id).flatMap { usr in
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let attach = Attachment(name: input.name, 
                                        kind: input.type,
                                        type: "image",
                                        url: resposne.secure_url,
                                        category: input.category,
                                        refID: resposne.public_id)
                return usr.$attachments.create(attach, on: req.db).transform(to: usr).flatMap { updatedUser in
                    if input.isProfile() {
                        updatedUser.profile = resposne.secure_url
                        return updatedUser.update(on: req.db).transform(to: updatedUser)
                    } else {
                        return req.eventLoop.future(updatedUser)
                    }
                }
            }
        }
    }
    
    fileprivate func createPhoneIfNeeded(req:Request, phone:String?, user:User) throws -> EventLoopFuture<User> {
        if let phone = phone {
            let phoneNumber = PhoneNumber(label: "main", number: phone.stripPhone())
            return user.$phones.create(phoneNumber, on: req.db).transform(to: user)
        } else {
            return req.eventLoop.future(user)
        }
    }
    
//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<User>, req:Request, org:String?) -> QueryBuilder<User> {
        let firstName:String?            = req.query["firstName"]
        let lastName:String?             = req.query["lastName"]
        let email:String?                = req.query["email"]
        let search:String?                = req.query["search"]
        
        if let nm = firstName  {
            query.filter(\.$firstName, .custom("ilike"), "%\(nm.lowercased())%")
        }
        
        if let nm = lastName  {
            query.filter(\.$lastName, .custom("ilike"), "%\(nm.lowercased())%")
        }
        
        if let search, !search.isEmpty  {
            query.group(.or) { orGroup in
                orGroup.filter(\.$firstName, .custom("ilike"), "%\(search.lowercased())%")
                orGroup.filter(\.$lastName, .custom("ilike"), "%\(search.lowercased())%")
            }
        }
        
        
        
        if let eml = email {
            query.filter(\.$email == eml)
        }
        
        let query = query
            .with(\.$attachments)
            .with(\.$phones)
            .with(\.$teams, { team in
                team.with(\.$households)
                team.with(\.$navigators)
            })
            .with(\.$org)
        
        if let orgId = org, !orgId.isEmpty {
            return query
                .join(parent: \.$org)
                .filter(Organization.self, \.$id == UUID(uuidString: orgId)!)
        } else {
            return query
        }
    }
}


//MARK: - CloudWatchLogs
extension UsersController {
    fileprivate func logDeleteMessage(navId: UUID, userId: String) -> String {
        return CloudWatchLogMessage.send(msg: .actions(type: .delete,
                                                          model: "Navigator",
                                                          by: navId.uuidString,
                                                          source: "NavigatorId: \(userId)"))
    }
}
