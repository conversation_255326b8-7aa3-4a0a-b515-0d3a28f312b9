//
//  ConsentCreatorController.swift
//  hmbl-core
//
//  Created by <PERSON> on 1/8/25.
//

import Vapor
import Fluent

struct ConsentCreatorController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let consentCreator = routes.grouped("admin", "consent-creator")
        
        // Apply admin authentication middleware
        let protected = consentCreator.grouped(AdminAuthMiddleware())
        
        protected.get(use: dashboard)
        protected.get("create", use: createForm)
        protected.post("create", use: createTemplate)
        protected.get(":templateID", "edit", use: editForm)
        protected.put(":templateID", use: updateTemplate)
        protected.delete(":templateID", use: deleteTemplate)
        protected.get(":templateID", "preview", use: previewTemplate)
    }
    
    func dashboard(req: Request) throws -> EventLoopFuture<View> {
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.query(on: req.db)
                .filter(\.$orgID == orgID)
                .filter(\.$kind == "consent_web")
                .sort(\.$createdAt, .descending)
                .all()
                .flatMap { templates in
                    let templateData = templates.map { template in
                        ConsentTemplateData(
                            id: template.id?.uuidString ?? "",
                            name: template.name,
                            key: template.key,
                            status: template.status,
                            language: template.language,
                            templateJson: template.template.template
                        )
                    }
                    
                    let context = ConsentCreatorDashboardContext(
                        adminUsername: req.session.data["admin_username"] ?? "Admin",
                        orgID: orgID,
                        templates: templateData
                    )
                    
                    return req.view.render("consent-creator-dashboard", context)
                }
        }
    }
    
    func createForm(req: Request) throws -> EventLoopFuture<View> {
        return try getUserOrganization(req: req).flatMap { orgID in
            let context = ConsentCreatorFormContext(
                adminUsername: req.session.data["admin_username"] ?? "Admin",
                orgID: orgID,
                isEdit: false,
                template: nil
            )
            
            return req.view.render("consent-creator-form", context)
        }
    }
    
    func createTemplate(req: Request) throws -> EventLoopFuture<Response> {
        let input = try req.content.decode(ConsentTemplateInput.self)
        
        return try getUserOrganization(req: req).flatMapThrowing { orgID in
            // Validate JSON structure
            guard let jsonData = input.templateJson.data(using: .utf8),
                  let _ = try? JSONSerialization.jsonObject(with: jsonData) else {
                throw Abort(.badRequest, reason: "Invalid JSON structure")
            }
            
            // Create template
            let template = Template(
                name: input.name,
                orgID: orgID,
                status: input.status,
                key: input.key,
                kind: "consent_web",
                language: input.language,
                template: JsonWrapper(input.templateJson),
                scored: false // Consent forms are not scored
            )
            
            return template
        }.flatMap { template in
            return template.save(on: req.db).map { _ in
                return req.redirect(to: "/admin/consent-creator")
            }
        }
    }
    
    func editForm(req: Request) throws -> EventLoopFuture<View> {
        guard let templateIDString = req.parameters.get("templateID"),
              let templateID = UUID(templateIDString) else {
            throw Abort(.badRequest, reason: "Invalid template ID")
        }
        
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.find(templateID, on: req.db)
                .flatMapThrowing { template in
                    guard let template = template else {
                        throw Abort(.notFound, reason: "Template not found")
                    }
                    
                    // Verify template belongs to user's organization and is consent type
                    guard template.orgID == orgID && template.kind == "consent_web" else {
                        throw Abort(.forbidden, reason: "Access denied")
                    }
                    
                    return template
                }
                .flatMap { template in
                    let context = ConsentCreatorFormContext(
                        adminUsername: req.session.data["admin_username"] ?? "Admin",
                        orgID: orgID,
                        isEdit: true,
                        template: ConsentTemplateData(
                            id: template.id?.uuidString ?? "",
                            name: template.name,
                            key: template.key,
                            status: template.status,
                            language: template.language,
                            templateJson: template.template.template
                        )
                    )
                    
                    return req.view.render("consent-creator-form", context)
                }
        }
    }
    
    func updateTemplate(req: Request) throws -> EventLoopFuture<Response> {
        guard let templateIDString = req.parameters.get("templateID"),
              let templateID = UUID(templateIDString) else {
            throw Abort(.badRequest, reason: "Invalid template ID")
        }
        
        let input = try req.content.decode(ConsentTemplateInput.self)
        
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.find(templateID, on: req.db)
                .flatMapThrowing { template in
                    guard let template = template else {
                        throw Abort(.notFound, reason: "Template not found")
                    }
                    
                    // Verify template belongs to user's organization and is consent type
                    guard template.orgID == orgID && template.kind == "consent_web" else {
                        throw Abort(.forbidden, reason: "Access denied")
                    }
                    
                    return template
                }
                .flatMap { template in
                    // Validate JSON structure
                    guard let jsonData = input.templateJson.data(using: .utf8),
                          let _ = try? JSONSerialization.jsonObject(with: jsonData) else {
                        return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Invalid JSON structure"))
                    }
                    
                    // Update template
                    template.name = input.name
                    template.status = input.status
                    template.key = input.key
                    template.language = input.language
                    template.template = JsonWrapper(input.templateJson)
                    
                    return template.save(on: req.db).map {
                        return req.redirect(to: "/admin/consent-creator")
                    }
                }
        }
    }
    
    func deleteTemplate(req: Request) throws -> EventLoopFuture<Response> {
        guard let templateIDString = req.parameters.get("templateID"),
              let templateID = UUID(templateIDString) else {
            throw Abort(.badRequest, reason: "Invalid template ID")
        }
        
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.find(templateID, on: req.db)
                .flatMapThrowing { template in
                    guard let template = template else {
                        throw Abort(.notFound, reason: "Template not found")
                    }
                    
                    // Verify template belongs to user's organization and is consent type
                    guard template.orgID == orgID && template.kind == "consent_web" else {
                        throw Abort(.forbidden, reason: "Access denied")
                    }
                    
                    return template
                }
                .flatMap { template in
                    return template.delete(on: req.db).map {
                        return req.redirect(to: "/admin/consent-creator")
                    }
                }
        }
    }
    
    func previewTemplate(req: Request) throws -> EventLoopFuture<ConsentPreviewData> {
        guard let templateIDString = req.parameters.get("templateID"),
              let templateID = UUID(templateIDString) else {
            throw Abort(.badRequest, reason: "Invalid template ID")
        }
        
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.find(templateID, on: req.db)
                .flatMapThrowing { template in
                    guard let template = template else {
                        throw Abort(.notFound, reason: "Template not found")
                    }
                    
                    // Verify template belongs to user's organization and is consent type
                    guard template.orgID == orgID && template.kind == "consent_web" else {
                        throw Abort(.forbidden, reason: "Access denied")
                    }
                    
                    return ConsentPreviewData(
                        id: template.id?.uuidString ?? "",
                        name: template.name,
                        templateJson: template.template.template
                    )
                }
        }
    }
    
    // Helper function to get user organization
    private func getUserOrganization(req: Request) throws -> EventLoopFuture<String> {
        return try AuthController.userFromToken(req: req).flatMapThrowing { user in
            guard let orgID = user.$org.id?.uuidString else {
                throw Abort(.badRequest, reason: "User organization not found")
            }
            return orgID.lowercased()
        }
    }
}

// MARK: - Data Transfer Objects

struct ConsentCreatorDashboardContext: Content {
    let adminUsername: String
    let orgID: String
    let templates: [ConsentTemplateData]
}

struct ConsentCreatorFormContext: Content {
    let adminUsername: String
    let orgID: String
    let isEdit: Bool
    let template: ConsentTemplateData?
}

struct ConsentTemplateData: Content {
    let id: String
    let name: String
    let key: String
    let status: String
    let language: String
    let templateJson: String
}

struct ConsentTemplateInput: Content {
    let name: String
    let key: String
    let status: String
    let language: String
    let templateJson: String
}

struct ConsentPreviewData: Content {
    let id: String
    let name: String
    let templateJson: String
}
