//
//  OrgProgramsController.swift
//  
//
//  Created by Augment Agent on 9/21/25.
//

import Foundation
import Fluent
import Vapor

struct OrgProgramsController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let orgPrograms = routes.grouped("orgs", ":orgID", "programs")

        // CRUD endpoints
        orgPrograms.post(use: createProgram)
        orgPrograms.get(use: getPrograms)
        orgPrograms.group(":programKey") { program in
            program.get(use: getProgram)
            program.put(use: updateProgram)
            program.delete(use: deleteProgram)
        }
    }
    
    // MARK: - Create Program
    func createProgram(req: Request) async throws -> OrgProgramResponse {
        guard let orgId = req.parameters.get("orgID") else {
            throw Abort(.badRequest, reason: "Invalid organization ID")
        }
        
        let createRequest = try req.content.decode(OrgProgramCreateRequest.self)
        
        // Check if program key already exists for this org
        let existingProgram = try await OrgProgram.query(on: req.db)
            .filter(\.$orgId == orgId)
            .filter(\.$programKey == createRequest.programKey)
            .first()
        
        if existingProgram != nil {
            throw Abort(.conflict, reason: "Program key already exists for this organization")
        }
        
        let orgProgram = OrgProgram(
            orgId: orgId,
            programName: createRequest.programName,
            programKey: createRequest.programKey,
            programConfig: createRequest.programConfig
        )
        
        try await orgProgram.save(on: req.db)
        return OrgProgramResponse(orgProgram: orgProgram)
    }
    
    // MARK: - Get Programs
    func getPrograms(req: Request) async throws -> [OrgProgramResponse] {
        guard let orgId = req.parameters.get("orgID") else {
            throw Abort(.badRequest, reason: "Invalid organization ID")
        }
        
        let programs = try await OrgProgram.query(on: req.db)
            .filter(\.$orgId == orgId)
            .sort(\.$createdAt, .descending)
            .all()
        
        return programs.map(OrgProgramResponse.init)
    }
    
    // MARK: - Get Single Program
    func getProgram(req: Request) async throws -> OrgProgramResponse {
        guard let orgId = req.parameters.get("orgID"),
              let programKey = req.parameters.get("programKey") else {
            throw Abort(.badRequest, reason: "Invalid organization ID or program key")
        }
        
        guard let program = try await OrgProgram.query(on: req.db)
            .filter(\.$orgId == orgId)
            .filter(\.$programKey == programKey)
            .first() else {
            throw Abort(.notFound, reason: "Program not found")
        }
        
        return OrgProgramResponse(orgProgram: program)
    }
    
    // MARK: - Update Program
    func updateProgram(req: Request) async throws -> OrgProgramResponse {
        guard let orgId = req.parameters.get("orgID"),
              let programKey = req.parameters.get("programKey") else {
            throw Abort(.badRequest, reason: "Invalid organization ID or program key")
        }
        
        guard let program = try await OrgProgram.query(on: req.db)
            .filter(\.$orgId == orgId)
            .filter(\.$programKey == programKey)
            .first() else {
            throw Abort(.notFound, reason: "Program not found")
        }
        
        let updateRequest = try req.content.decode(OrgProgramUpdateRequest.self)
        
        if let programName = updateRequest.programName {
            program.programName = programName
        }
        
        if let programConfig = updateRequest.programConfig {
            program.programConfig = programConfig
        }
        
        try await program.save(on: req.db)
        return OrgProgramResponse(orgProgram: program)
    }
    
    // MARK: - Delete Program
    func deleteProgram(req: Request) async throws -> HTTPStatus {
        guard let orgId = req.parameters.get("orgID"),
              let programKey = req.parameters.get("programKey") else {
            throw Abort(.badRequest, reason: "Invalid organization ID or program key")
        }
        
        guard let program = try await OrgProgram.query(on: req.db)
            .filter(\.$orgId == orgId)
            .filter(\.$programKey == programKey)
            .first() else {
            throw Abort(.notFound, reason: "Program not found")
        }
        
        try await program.delete(on: req.db)
        return .noContent
    }
}
