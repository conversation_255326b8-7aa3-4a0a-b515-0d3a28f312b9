//
//  AssessmentCreatorController.swift
//  hmbl-core
//
//  Created by Augment Agent on 2025-09-07.
//

import Foundation
import Vapor
import Fluent

struct AssessmentCreatorController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let admin = routes.grouped("admin")
        let protected = admin.grouped(AdminAuthMiddleware())
        
        // Assessment Creator routes
        protected.get("assessment-creator", use: dashboard)
        protected.get("assessment-creator", "templates", use: listTemplates)
        protected.get("assessment-creator", "create", use: showCreateForm)
        protected.post("assessment-creator", "create", use: createTemplate)
        protected.get("assessment-creator", ":templateID", "edit", use: showEditForm)
        protected.put("assessment-creator", ":templateID", use: updateTemplate)
        protected.delete("assessment-creator", ":templateID", use: deleteTemplate)
        protected.get("assessment-creator", ":templateID", "preview", use: previewTemplate)
        protected.post("assessment-creator", ":templateID", "duplicate", use: duplicateTemplate)
    }
    
    // MARK: - Dashboard
    func dashboard(req: Request) throws -> EventLoopFuture<View> {
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.query(on: req.db)
                .filter(\.$orgID == orgID)
                .filter(\.$kind == "assessment")
                .sort(\.$updatedAt, .descending)
                .all()
                .flatMap { templates in
                    let context = AssessmentCreatorDashboardContext(
                        adminUsername: req.session.data["admin_username"] ?? "Admin",
                        orgID: orgID,
                        templates: templates.map { template in
                            AssessmentTemplateInfo(
                                id: template.id?.uuidString ?? "",
                                name: template.name,
                                key: template.key,
                                status: template.status,
                                language: template.language,
                                scored: template.scored,
                                createdAt: template.createdAt?.formatted() ?? "",
                                updatedAt: template.updatedAt?.formatted() ?? ""
                            )
                        }
                    )
                    
                    return req.view.render("assessment-creator-dashboard", context)
                }
        }
    }
    
    // MARK: - List Templates
    func listTemplates(req: Request) throws -> EventLoopFuture<[AssessmentTemplateInfo]> {
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.query(on: req.db)
                .filter(\.$orgID == orgID)
                .filter(\.$kind == "assessment")
                .sort(\.$updatedAt, .descending)
                .all()
                .map { templates in
                    templates.map { template in
                        AssessmentTemplateInfo(
                            id: template.id?.uuidString ?? "",
                            name: template.name,
                            key: template.key,
                            status: template.status,
                            language: template.language,
                            scored: template.scored,
                            createdAt: template.createdAt?.formatted() ?? "",
                            updatedAt: template.updatedAt?.formatted() ?? ""
                        )
                    }
                }
        }
    }
    
    // MARK: - Create Template
    func showCreateForm(req: Request) throws -> EventLoopFuture<View> {
        return try getUserOrganization(req: req).flatMap { orgID in
            let context = AssessmentCreatorFormContext(
                adminUsername: req.session.data["admin_username"] ?? "Admin",
                orgID: orgID,
                isEdit: false,
                template: nil
            )
            
            return req.view.render("assessment-creator-form", context)
        }
    }
    
    func createTemplate(req: Request) throws -> EventLoopFuture<Response> {
        let input = try req.content.decode(AssessmentTemplateInput.self)

        return try getUserOrganization(req: req).flatMapThrowing { orgID in
            // Validate JSON structure
            guard let jsonData = input.templateJson.data(using: .utf8),
                  let _ = try? JSONSerialization.jsonObject(with: jsonData) else {
                throw Abort(.badRequest, reason: "Invalid JSON structure")
            }

            // Create template
            let template = Template(
                name: input.name,
                orgID: orgID,
                status: input.status,
                key: input.key,
                kind: "assessment",
                language: input.language,
                template: JsonWrapper(input.templateJson),
                scored: input.scored
            )

            return template
        }.flatMap { template in
            return template.save(on: req.db).map { _ in
                return req.redirect(to: "/admin/assessment-creator")
            }
        }
    }
    
    // MARK: - Edit Template
    func showEditForm(req: Request) throws -> EventLoopFuture<View> {
        guard let templateIDString = req.parameters.get("templateID"),
              let templateID = UUID(templateIDString) else {
            throw Abort(.badRequest, reason: "Invalid template ID")
        }
        
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.find(templateID, on: req.db)
                .flatMapThrowing { template in
                    guard let template = template else {
                        throw Abort(.notFound, reason: "Template not found")
                    }
                    
                    // Verify template belongs to user's organization
                    guard template.orgID == orgID else {
                        throw Abort(.forbidden, reason: "Access denied")
                    }
                    
                    return template
                }
                .flatMap { template in
                    let context = AssessmentCreatorFormContext(
                        adminUsername: req.session.data["admin_username"] ?? "Admin",
                        orgID: orgID,
                        isEdit: true,
                        template: AssessmentTemplateData(
                            id: template.id?.uuidString ?? "",
                            name: template.name,
                            key: template.key,
                            status: template.status,
                            language: template.language,
                            scored: template.scored,
                            templateJson: template.template.template
                        )
                    )
                    
                    return req.view.render("assessment-creator-form", context)
                }
        }
    }
    
    func updateTemplate(req: Request) throws -> EventLoopFuture<Response> {
        guard let templateIDString = req.parameters.get("templateID"),
              let templateID = UUID(templateIDString) else {
            throw Abort(.badRequest, reason: "Invalid template ID")
        }
        
        let input = try req.content.decode(AssessmentTemplateInput.self)
        
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.find(templateID, on: req.db)
                .flatMapThrowing { template in
                    guard let template = template else {
                        throw Abort(.notFound, reason: "Template not found")
                    }
                    
                    // Verify template belongs to user's organization
                    guard template.orgID == orgID else {
                        throw Abort(.forbidden, reason: "Access denied")
                    }
                    
                    return template
                }
                .flatMap { template in
                    // Validate JSON structure
                    guard let jsonData = input.templateJson.data(using: .utf8),
                          let _ = try? JSONSerialization.jsonObject(with: jsonData) else {
                        return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Invalid JSON structure"))
                    }
                    
                    // Update template
                    template.name = input.name
                    template.status = input.status
                    template.key = input.key
                    template.language = input.language
                    template.template = JsonWrapper(input.templateJson)
                    template.scored = input.scored
                    
                    return template.save(on: req.db).map {
                        return req.redirect(to: "/admin/assessment-creator")
                    }
                }
        }
    }
    
    // MARK: - Delete Template
    func deleteTemplate(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let templateIDString = req.parameters.get("templateID"),
              let templateID = UUID(templateIDString) else {
            throw Abort(.badRequest, reason: "Invalid template ID")
        }
        
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.find(templateID, on: req.db)
                .flatMapThrowing { template in
                    guard let template = template else {
                        throw Abort(.notFound, reason: "Template not found")
                    }
                    
                    // Verify template belongs to user's organization
                    guard template.orgID == orgID else {
                        throw Abort(.forbidden, reason: "Access denied")
                    }
                    
                    return template
                }
                .flatMap { template in
                    return template.delete(on: req.db).transform(to: HTTPStatus.ok)
                }
        }
    }
    
    // MARK: - Preview Template
    func previewTemplate(req: Request) throws -> EventLoopFuture<AssessmentPreviewData> {
        guard let templateIDString = req.parameters.get("templateID"),
              let templateID = UUID(templateIDString) else {
            throw Abort(.badRequest, reason: "Invalid template ID")
        }
        
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.find(templateID, on: req.db)
                .flatMapThrowing { template in
                    guard let template = template else {
                        throw Abort(.notFound, reason: "Template not found")
                    }
                    
                    // Verify template belongs to user's organization
                    guard template.orgID == orgID else {
                        throw Abort(.forbidden, reason: "Access denied")
                    }
                    
                    return AssessmentPreviewData(
                        id: template.id?.uuidString ?? "",
                        name: template.name,
                        templateJson: template.template.template
                    )
                }
        }
    }
    
    // MARK: - Duplicate Template
    func duplicateTemplate(req: Request) throws -> EventLoopFuture<Response> {
        guard let templateIDString = req.parameters.get("templateID"),
              let templateID = UUID(templateIDString) else {
            throw Abort(.badRequest, reason: "Invalid template ID")
        }
        
        return try getUserOrganization(req: req).flatMap { orgID in
            return Template.find(templateID, on: req.db)
                .flatMapThrowing { template in
                    guard let template = template else {
                        throw Abort(.notFound, reason: "Template not found")
                    }
                    
                    // Verify template belongs to user's organization
                    guard template.orgID == orgID else {
                        throw Abort(.forbidden, reason: "Access denied")
                    }
                    
                    return template
                }
                .flatMap { originalTemplate in
                    // Create duplicate with modified name and key
                    let duplicateTemplate = Template(
                        name: "\(originalTemplate.name) (Copy)",
                        orgID: orgID,
                        status: "draft",
                        key: "\(originalTemplate.key)_copy_\(Int(Date().timeIntervalSince1970))",
                        kind: "assessment",
                        language: originalTemplate.language,
                        template: originalTemplate.template,
                        scored: originalTemplate.scored
                    )
                    
                    return duplicateTemplate.save(on: req.db).map {
                        return req.redirect(to: "/admin/assessment-creator")
                    }
                }
        }
    }
    
    // MARK: - Helper Methods
    private func getUserOrganization(req: Request) throws -> EventLoopFuture<String> {
        return try AuthController.userFromToken(req: req).flatMapThrowing { user in
            guard let orgID = user.$org.id?.uuidString else {
                throw Abort(.badRequest, reason: "User organization not found")
            }
            return orgID.lowercased()
        }
    }
}

// MARK: - Data Structures

struct AssessmentCreatorDashboardContext: Content {
    let adminUsername: String
    let orgID: String
    let templates: [AssessmentTemplateInfo]
}

struct AssessmentTemplateInfo: Content {
    let id: String
    let name: String
    let key: String
    let status: String
    let language: String
    let scored: Bool
    let createdAt: String
    let updatedAt: String
}

struct AssessmentCreatorFormContext: Content {
    let adminUsername: String
    let orgID: String
    let isEdit: Bool
    let template: AssessmentTemplateData?
}

struct AssessmentTemplateData: Content {
    let id: String
    let name: String
    let key: String
    let status: String
    let language: String
    let scored: Bool
    let templateJson: String
}

struct AssessmentTemplateInput: Content {
    let name: String
    let key: String
    let status: String
    let language: String
    let scored: Bool
    let templateJson: String
}

struct AssessmentPreviewData: Content {
    let id: String
    let name: String
    let templateJson: String
}

// MARK: - Assessment JSON Structure Models

struct AssessmentTemplate: Codable {
    let name: String
    let banner: String?
    let legend: [AssessmentLegend]?
    let sections: [AssessmentSection]
}

struct AssessmentLegend: Codable {
    let headers: [String]
    let rows: [[String]]
}

struct AssessmentSection: Codable {
    let type: String
    let title: String
    let complete: Bool
    let questions: [AssessmentQuestion]
}

struct AssessmentQuestion: Codable {
    let title: String?
    let message: String
    let level: Int
    let value: Int
    let score: Int?
    let type: String
    let questions: [AssessmentQuestion]?
}

// MARK: - Date Extension
extension Date {
    func formatted() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: self)
    }
}
