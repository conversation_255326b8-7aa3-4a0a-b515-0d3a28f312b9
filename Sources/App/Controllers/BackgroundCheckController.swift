//
//  BackgroundCheckController.swift
//  
//
//  Created by <PERSON> on 9/9/25.
//

import Foundation
import Fluent
import Vapor

struct BackgroundCheckController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let backgroundChecks = routes.grouped("api", "background-checks")
        
        // Admin-only routes with token authentication
        let adminProtected = backgroundChecks.grouped(Token.authenticator())
        
        // Trigger background check for a member
        adminProtected.post("members", ":memberID", "trigger", use: triggerBackgroundCheck)

        // Trigger background check for a household
        adminProtected.post("households", ":householdID", "trigger", use: triggerHouseholdBackgroundCheck)

        // Get review queue
        adminProtected.get("queue", use: getReviewQueue)
        
        // Get specific queue item
        adminProtected.get("queue", ":queueID", use: getQueueItem)

        // Location-based search
        adminProtected.post("location-search", use: locationSearch)

        // Approve background check
        adminProtected.post("queue", ":queueID", "approve", use: approveBackgroundCheck)

        // Reject background check
        adminProtected.post("queue", ":queueID", "reject", use: rejectBackgroundCheck)
    }
    
    // MARK: - Trigger Background Check
    func triggerBackgroundCheck(req: Request) throws -> EventLoopFuture<BackgroundCheckTriggerResponse> {
        // Get member ID from parameters
        guard let memberIDString = req.parameters.get("memberID"),
              let memberID = UUID(uuidString: memberIDString) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        // Decode and validate request body
        let input = try req.content.decode(BackgroundCheckTriggerInput.self)
        try BackgroundCheckTriggerInput.validate(content: req)
        
        // Get current user and verify admin access
        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                // Find the member
                return Member.find(memberID, on: req.db).flatMap { member in
                    guard let member = member else {
                        return req.eventLoop.makeFailedFuture(Abort(.notFound, reason: "Member not found"))
                    }
                    
                    // Check if there's already a pending background check for this member
                    return BackgroundCheckReviewQueue.query(on: req.db)
                        .filter(\.$member.$id == memberID)
                        .filter(\.$reviewStatus == .pending)
                        .first()
                        .flatMap { existingPendingCheck in
//                            if existingPendingCheck != nil {
//                                return req.eventLoop.makeFailedFuture(Abort(.conflict, reason: "A background check is already pending for this member"))
//                            }
                            
                            // Call Zyla API (simplified for EventLoopFuture pattern)
                            return self.performBackgroundCheck(
                                req: req,
                                member: member,
                                currentUser: currentUser,
                                input: input
                            )
                        }
                }
            }
        }
    }

    // MARK: - Trigger Household Background Check
    func triggerHouseholdBackgroundCheck(req: Request) throws -> EventLoopFuture<HouseholdBackgroundCheckResponse> {
        // Get household ID from parameters
        guard let householdIDString = req.parameters.get("householdID"),
              let householdID = UUID(uuidString: householdIDString) else {
            throw Abort(.badRequest, reason: "Invalid household ID")
        }

        // Decode and validate request body
        let input = try req.content.decode(HouseholdBackgroundCheckInput.self)
        try HouseholdBackgroundCheckInput.validate(content: req)

        // Get current user and verify admin access
        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                // Find the household with members
                return Household.query(on: req.db)
                    .with(\.$members)
                    .filter(\.$id == householdID)
                    .first()
                    .flatMap { household -> EventLoopFuture<HouseholdBackgroundCheckResponse> in
                        guard let household = household else {
                            return req.eventLoop.makeFailedFuture(Abort(.notFound, reason: "Household not found"))
                        }

                        // Check if there's already a pending background check for this household
                        return BackgroundCheckReviewQueue.query(on: req.db)
                            .filter(\.$household.$id == householdID)
                            .filter(\.$reviewStatus == .pending)
                            .first()
                            .flatMap { existingPendingCheck -> EventLoopFuture<HouseholdBackgroundCheckResponse> in
                                if existingPendingCheck != nil {
                                    return req.eventLoop.makeFailedFuture(Abort(.conflict, reason: "A background check is already pending for this household"))
                                }

                                // Query household members directly and filter for adults (18+)
                                return Member.query(on: req.db)
                                    .join(HouseholdMembers.self, on: \Member.$id == \HouseholdMembers.$member.$id)
                                    .filter(HouseholdMembers.self, \.$household.$id == householdID)
                                    .all()
                                    .flatMap { allMembers in
                                        // For now, use all members (TODO: Add age filtering)
                                        if allMembers.isEmpty {
                                            return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "No members found in household"))
                                        }

                                        // Perform background checks for all household members
                                        return self.performHouseholdBackgroundCheck(
                                            req: req,
                                            household: household,
                                            adultMembers: allMembers,
                                            currentUser: currentUser,
                                            input: input
                                        )
                                    }
                            }
                    }
            }
        }
    } 
    
    // MARK: - Location-Based Search
    func locationSearch(req: Request) throws -> EventLoopFuture<LocationSearchResponse> {
        req.logger.info("Location search endpoint called")

        let input: LocationSearchInput
        do {
            input = try req.content.decode(LocationSearchInput.self)
            if let memberId = input.memberId {
                req.logger.info("Decoded input: memberId=\(memberId), address=\(input.address)")
            } else if let householdId = input.householdId  {
                req.logger.info("Decoded input: householdId=\(householdId), address=\(input.address)")
            }
        } catch {
            req.logger.error("Failed to decode LocationSearchInput: \(error)")
            return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Invalid request data"))
        }

        return (try? AuthController.userFromToken(req: req))?.flatMap { currentUser in
            req.logger.info("User authenticated")

            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                req.logger.info("Admin access verified")

                // Create Zyla service
                let zylaService = ZylaOffenderRegistryService(
                    client: req.application.http.client.shared,
                    apiKey: Environment.get("ZYLA_API_KEY") ?? ""
                )

                // Perform location-based search
                return req.eventLoop.makeFutureWithTask {
                    try await zylaService.searchOffendersByLocation(
                        address: input.address,
                        latitude: input.latitude,
                        longitude: input.longitude
                    )
                }.flatMap { locationResponse in
                    req.logger.info("Got location response with \(locationResponse.totalCount) total offenders")

                    // Create queue item for location-based search
                    if let memberId = input.memberId {
                        let queueItem = BackgroundCheckReviewQueue(
                            memberId: memberId, // Use the actual memberId from input
                            triggeredById: try! currentUser.requireID(),
                            fullName: "Location Search: \(input.address)",
                            zipCode: self.extractZipCode(from: input.address) ?? "Unknown",
                            rawResponse: self.convertLocationToStandardResponse(locationResponse),
                            matchedCount: locationResponse.totalCount,
                            highestRiskLevel: self.determineHighestRiskLevel(from: locationResponse),
                            requiresReview: locationResponse.totalCount > 0
                        )

                        return queueItem.save(on: req.db).map { _ in
                            return LocationSearchResponse(
                                queueId: queueItem.id!,
                                exactMatches: locationResponse.exactMatches,
                                nearbyOffenders: locationResponse.nearbyOffenders,
                                totalCount: locationResponse.totalCount,
                                searchQuery: locationResponse.searchQuery
                            )
                        }
                    } else if let householdId = input.householdId {
                        let queueItem = BackgroundCheckReviewQueue(
                            householdId: householdId,
                            triggeredById: try! currentUser.requireID(),
                            fullName: "Location Search: \(input.address)",
                            zipCode: self.extractZipCode(from: input.address) ?? "Unknown",
                            rawResponse: self.convertLocationToStandardResponse(locationResponse),
                            matchedCount: locationResponse.totalCount,
                            highestRiskLevel: self.determineHighestRiskLevel(from: locationResponse),
                            requiresReview: locationResponse.totalCount > 0
                        )

                        return queueItem.save(on: req.db).map { _ in
                            return LocationSearchResponse(
                                queueId: queueItem.id!,
                                exactMatches: locationResponse.exactMatches,
                                nearbyOffenders: locationResponse.nearbyOffenders,
                                totalCount: locationResponse.totalCount,
                                searchQuery: locationResponse.searchQuery
                            )
                        }
                    } else {
                        return req.eventLoop.future(LocationSearchResponse(queueId: UUID(),
                                                                           exactMatches: [],
                                                                           nearbyOffenders: [],
                                                                           totalCount: 0,
                                                                           searchQuery: ZylaLocationQuery(address: input.address,
                                                                                                          latitude: input.latitude,
                                                                                                          longitude: input.longitude,
                                                                                                          radiusMiles: 2.0)))
                    }
                }
            }
        } ?? req.eventLoop.makeFailedFuture(Abort(.unauthorized, reason: "Authentication failed"))
    }
  
    // MARK: - Get Review Queue
    func getReviewQueue(req: Request) throws -> EventLoopFuture<Page<BackgroundCheckQueueItem>> {
        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                let query = BackgroundCheckReviewQueue.query(on: req.db)
                    .filter(\.$reviewStatus == .pending)
                    .with(\.$member)
                    .with(\.$household)
                    .with(\.$triggeredBy)
                    .sort(\.$createdAt, .descending)

                return query.paginate(for: req).map { page in
                    let items = page.items.map(self.convertToQueueItem)
                    return Page(items: items, metadata: page.metadata)
                }
            }
        }
    }

    // MARK: - Helper to convert queue item (breaks up complex expression)
    private func convertToQueueItem(_ queueItem: BackgroundCheckReviewQueue) -> BackgroundCheckQueueItem {
        let memberSummary: MemberSummary? = queueItem.member.map { member in
            MemberSummary(
                id: member.id!,
                firstName: member.firstName,
                lastName: member.lastName,
                email: member.email
            )
        }

        let householdSummary: HouseholdSummary? = queueItem.household.map { household in
            HouseholdSummary(
                id: household.id!,
                name: household.title,
                memberCount: household.members.count
            )
        }

        let triggeredBySummary = UserSummary(
            id: queueItem.triggeredBy.id!,
            firstName: queueItem.triggeredBy.firstName,
            lastName: queueItem.triggeredBy.lastName,
            email: queueItem.triggeredBy.email
        )

        return BackgroundCheckQueueItem(
            id: queueItem.id!,
            member: memberSummary,
            household: householdSummary,
            targetType: queueItem.targetType,
            triggeredBy: triggeredBySummary,
            fullName: queueItem.fullName,
            zipCode: queueItem.zipCode,
            matchedCount: queueItem.matchedCount,
            highestRiskLevel: queueItem.highestRiskLevel,
            createdAt: queueItem.createdAt!,
            rawResponse: queueItem.rawResponse
        )
    }
    
    // MARK: - Get Queue Item
    func getQueueItem(req: Request) throws -> EventLoopFuture<BackgroundCheckQueueItem> {
        guard let queueIDString = req.parameters.get("queueID"),
              let queueID = UUID(uuidString: queueIDString) else {
            throw Abort(.badRequest, reason: "Invalid queue item ID")
        }
        
        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                return BackgroundCheckReviewQueue.find(queueID, on: req.db).flatMap { queueItem in
                    guard let queueItem = queueItem else {
                        return req.eventLoop.makeFailedFuture(Abort(.notFound, reason: "Queue item not found")) as EventLoopFuture<BackgroundCheckQueueItem>
                    }
                    
                    // Load both member and household relationships
                    return queueItem.$member.load(on: req.db).flatMap { _ in
                        return queueItem.$household.load(on: req.db).flatMap { _ in
                            return queueItem.$triggeredBy.load(on: req.db).map { _ in
                                req.logger.info("Queue item rawResponse: \(queueItem.rawResponse)")
                                req.logger.info("Queue item rawResponse results count: \(queueItem.rawResponse.results.count)")

                                let response = BackgroundCheckQueueItem(
                                    id: queueItem.id!,
                                    member: queueItem.member.map { member in
                                        MemberSummary(
                                            id: member.id!,
                                            firstName: member.firstName,
                                            lastName: member.lastName,
                                            email: member.email
                                        )
                                    },
                                    household: queueItem.household.map { household in
                                        HouseholdSummary(
                                            id: household.id!,
                                            name: household.title,
                                            memberCount: household.members.count
                                        )
                                    },
                                    targetType: queueItem.targetType,
                                    triggeredBy: UserSummary(
                                        id: queueItem.triggeredBy.id!,
                                        firstName: queueItem.triggeredBy.firstName,
                                        lastName: queueItem.triggeredBy.lastName,
                                        email: queueItem.triggeredBy.email
                                    ),
                                    fullName: queueItem.fullName,
                                    zipCode: queueItem.zipCode,
                                    matchedCount: queueItem.matchedCount,
                                    highestRiskLevel: queueItem.highestRiskLevel,
                                    createdAt: queueItem.createdAt!,
                                    rawResponse: queueItem.rawResponse
                                )

                                req.logger.info("Returning response with rawResponse: \(response.rawResponse)")
                                return response
                            }
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    private func verifyAdminAccess(req: Request, user: User) -> EventLoopFuture<Void> {
        return req.eventLoop.future().flatMapThrowing { _ in
            // Check if user has admin role
            guard let roles = user.roles,
                  roles.contains("drop_all") ||
                  roles.contains(where: { $0.localizedCaseInsensitiveContains("admin") })
            else {
                req.logger.error("verifyAdminAccess - Admin access required: user roles = \(user.roles ?? [])")
                throw Abort(.forbidden, reason: "Admin access required")
            }
            
            req.logger.info("verifyAdminAccess - Success for user \(user.email)")
            return ()
        }
    }

    // MARK: - Household Background Check Logic
    private func performHouseholdBackgroundCheck(
        req: Request,
        household: Household,
        adultMembers: [Member],
        currentUser: User,
        input: HouseholdBackgroundCheckInput
    ) -> EventLoopFuture<HouseholdBackgroundCheckResponse> {
        req.logger.info("Starting household background check for household: \(household.id?.uuidString ?? "unknown")")

        var memberResults: [MemberBackgroundCheckResult] = []
        var queueItemIds: [UUID] = []
        var totalMatches = 0
        var requiresReview = false

        // Create individual background check inputs for each adult member
        let memberChecks = adultMembers.map { member in
            let memberInput = BackgroundCheckTriggerInput(
                fullName: "\(member.lastName), \(member.firstName)",
                zipCode: input.zipCode
            )

            return self.performBackgroundCheck(
                req: req,
                member: member,
                currentUser: currentUser,
                input: memberInput
            ).map { response in
                let result = MemberBackgroundCheckResult(
                    memberId: try! member.requireID(),
                    memberName: "\(member.firstName) \(member.lastName)",
                    matchesFound: response.matchesFound,
                    requiresReview: response.requiresReview,
                    queueItemId: response.queueItemId,
                    success: response.success,
                    error: response.success ? nil : response.message
                )

                if response.success {
                    totalMatches += response.matchesFound
                    if response.requiresReview {
                        requiresReview = true
                    }
                    if let queueId = response.queueItemId {
                        queueItemIds.append(queueId)
                    }
                }

                return result
            }.recover { error in
                // Handle individual member check failures
                return MemberBackgroundCheckResult(
                    memberId: try! member.requireID(),
                    memberName: "\(member.firstName) \(member.lastName)",
                    matchesFound: 0,
                    requiresReview: false,
                    queueItemId: nil,
                    success: false,
                    error: error.localizedDescription
                )
            }
        }

        // Wait for all member checks to complete
        return EventLoopFuture.whenAllComplete(memberChecks, on: req.eventLoop).map { results in
            memberResults = results.compactMap { result in
                switch result {
                case .success(let memberResult):
                    return memberResult
                case .failure(let error):
                    req.logger.error("Member background check failed: \(error)")
                    return nil
                }
            }

            let successfulChecks = memberResults.filter { $0.success }.count
            let message: String

            if successfulChecks == adultMembers.count {
                if totalMatches > 0 {
                    message = "Household background check completed. \(totalMatches) total matches found across \(successfulChecks) members."
                } else {
                    message = "Household background check completed. No matches found for any household members."
                }
            } else {
                message = "Household background check completed with \(adultMembers.count - successfulChecks) failures. \(totalMatches) matches found."
            }

            return HouseholdBackgroundCheckResponse(
                success: successfulChecks > 0,
                message: message,
                householdId: try! household.requireID(),
                totalMembers: adultMembers.count,
                checkedMembers: successfulChecks,
                totalMatches: totalMatches,
                requiresReview: requiresReview,
                queueItemIds: queueItemIds,
                memberResults: memberResults
            )
        }
    }

    // MARK: - Location Search Helper Methods
    private func extractZipCode(from address: String) -> String? {
        let zipPattern = "\\b\\d{5}\\b"
        let regex = try? NSRegularExpression(pattern: zipPattern)
        let range = NSRange(address.startIndex..<address.endIndex, in: address)

        if let match = regex?.firstMatch(in: address, range: range) {
            return String(address[Range(match.range, in: address)!])
        }

        return nil
    }

    private func convertLocationToStandardResponse(_ locationResponse: ZylaLocationResponse) -> ZylaOffenderResponse {
        // Combine exact matches and nearby offenders into a single results array
        let allOffenders = locationResponse.exactMatches + locationResponse.nearbyOffenders

        // Create a standard search query from location query
        let standardQuery = ZylaSearchQuery(
            name: "Location Search",
            zipCode: extractZipCode(from: locationResponse.searchQuery.address) ?? "Unknown"
        )

        return ZylaOffenderResponse(
            results: allOffenders,
            searchQuery: standardQuery,
            timestamp: locationResponse.timestamp
        )
    }

    private func determineHighestRiskLevel(from locationResponse: ZylaLocationResponse) -> String? {
        let allOffenders = locationResponse.exactMatches + locationResponse.nearbyOffenders
        let riskLevels = allOffenders.compactMap { $0.riskLevel }

        // Priority: Level III > Level II > Level I
        if riskLevels.contains(where: { $0.contains("III") }) {
            return "Level III"
        } else if riskLevels.contains(where: { $0.contains("II") }) {
            return "Level II"
        } else if riskLevels.contains(where: { $0.contains("I") }) {
            return "Level I"
        }

        return riskLevels.first
    }

    // MARK: - Approve Background Check
    func approveBackgroundCheck(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let queueIDString = req.parameters.get("queueID"),
              let queueID = UUID(uuidString: queueIDString) else {
            throw Abort(.badRequest, reason: "Invalid queue item ID")
        }
        
        let input = try req.content.decode(BackgroundCheckReviewInput.self)
        
        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                return BackgroundCheckReviewQueue.find(queueID, on: req.db).flatMap { queueItem in
                    guard let queueItem = queueItem else {
                        return req.eventLoop.makeFailedFuture(Abort(.notFound, reason: "Queue item not found"))
                    }
                    
                    guard queueItem.reviewStatus == .pending else {
                        return req.eventLoop.future(error: Abort(.conflict, reason: "Queue item has already been reviewed"))
                    }
                    
                    // Validate that an offender was selected
                    guard let selectedIndex = input.selectedOffenderIndex else {
                        return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "No offender selected for approval"))
                    }
                    
                    guard selectedIndex >= 0 && selectedIndex < queueItem.rawResponse.results.count else {
                        return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Invalid offender selection"))
                    }
                    
                    // Update queue item
                    queueItem.reviewStatus = .approved
                    queueItem.$reviewedBy.id = try! currentUser.requireID()
                    queueItem.reviewedAt = Date()
                    queueItem.reviewNotes = input.notes
                    queueItem.selectedOffenderIndex = selectedIndex
                    queueItem.matchSelections = input.matchSelections
                    
                    return queueItem.save(on: req.db).flatMap { _ in
                        return queueItem.$member.load(on: req.db).flatMap { _ in
                            // Get only the selected offender record
                            let selectedOffender = queueItem.rawResponse.results[selectedIndex]
                            
                            // Add to member's security profile using only the selected offender
                            let backgroundCheck = BackgroundCheck(
                                status: "flagged",
                                matchType: "verified_match",
                                reviewStatus: "approved",
                                reviewedBy: try! currentUser.requireID(),
                                requiresReview: false
                            )

                            // Only process member-based background checks for now
                            guard let member = queueItem.member else {
                                return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Household background checks not yet supported in approval workflow"))
                            }

                            member.addBackgroundCheck(backgroundCheck)

                            return member.save(on: req.db).flatMap { _ in
                                // Create timeline item
                                return self.createTimelineItem(
                                    req: req,
                                    memberId: try! member.requireID(),
                                    creatorId: try! currentUser.requireID(),
                                    title: "Background Check Verified",
                                    description: "Background check approved by admin. Selected match: \(selectedOffender.name). Security flags applied.",
                                    status: "background_check_approved",
                                    visible: true
                                ).flatMap { _ in
                                    // Apply security tags
                                    return self.applySecurityTags(
                                        req: req,
                                        member: member,
                                        queueItem: queueItem,
                                        currentUser: currentUser
                                    ).map { _ in
                                        return .ok
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    private func performBackgroundCheck(
        req: Request,
        member: Member,
        currentUser: User,
        input: BackgroundCheckTriggerInput
    ) -> EventLoopFuture<BackgroundCheckTriggerResponse> {
        // Validate API key is available
        guard let apiKey = Environment.get("ZYLA_API_KEY"), !apiKey.isEmpty else {
            req.logger.error("ZYLA_API_KEY environment variable is not set or empty")
            return req.eventLoop.makeFailedFuture(
                Abort(.internalServerError, reason: "Background check service is not properly configured")
            )
        }
        
        // Create Zyla service with API key from environment
        let zylaService = ZylaOffenderRegistryService(
            client: req.application.http.client.shared,
            apiKey: apiKey
        )
        
        // Log the background check attempt
        req.logger.info("Performing background check for member: \(member.id?.uuidString ?? "unknown"), name: \(input.fullName), zipCode: \(input.zipCode)")
        
        // Perform the actual API call
        return req.eventLoop.makeFutureWithTask {
            do {
                let zylaResponse = try await zylaService.searchOffenders(
                    name: input.fullName,
                    zipCode: input.zipCode
                )
                
                req.logger.info("Background check API call successful. Found \(zylaResponse.results.count) results")
                return zylaResponse
            } catch let error as ZylaAPIError {
                req.logger.error("Zyla API error: \(error.localizedDescription)")
                
                // Handle specific API errors gracefully
                switch error {
                case .unauthorized:
                    throw Abort(.internalServerError, reason: "Background check service authentication failed")
                case .rateLimitExceeded:
                    throw Abort(.tooManyRequests, reason: "Background check service is temporarily unavailable due to rate limiting")
                case .serviceUnavailable:
                    throw Abort(.serviceUnavailable, reason: "Background check service is currently unavailable")
                case .invalidInput(let message):
                    throw Abort(.badRequest, reason: "Invalid input for background check: \(message)")
                default:
                    throw Abort(.internalServerError, reason: "Background check service error: \(error.localizedDescription)")
                }
            } catch {
                req.logger.error("Background check API call failed with unexpected error: \(error)")
                throw Abort(.internalServerError, reason: "Background check service encountered an unexpected error")
            }
        }.flatMap { zylaResponse in
            let matchedCount = zylaResponse.results.count
            
            // Analyze the response to get risk level information
            let analysis = zylaService.analyzeResponse(zylaResponse)
            
            if matchedCount > 0 {
                // Create queue item for review
                let queueItem = BackgroundCheckReviewQueue(
                    memberId: try! member.requireID(),
                    triggeredById: try! currentUser.requireID(),
                    fullName: input.fullName,
                    zipCode: input.zipCode,
                    rawResponse: zylaResponse,
                    matchedCount: matchedCount,
                    highestRiskLevel: analysis.highestRiskLevel,
                    requiresReview: analysis.requiresReview,
                    reviewStatus: .pending
                )
                
                return queueItem.save(on: req.db).flatMap { _ in
                    return self.createTimelineItem(
                        req: req,
                        memberId: try! member.requireID(),
                        creatorId: try! currentUser.requireID(),
                        title: "Background Check – Match Found",
                        description: "Background check found \(matchedCount) potential match(es). Review required.",
                        status: "background_check_match",
                        visible: false
                    ).map { _ in
                        return BackgroundCheckTriggerResponse(
                            success: true,
                            message: "Background check completed. \(matchedCount) match(es) found - review required.",
                            matchesFound: matchedCount,
                            requiresReview: true,
                            queueItemId: queueItem.id
                        )
                    }
                }
            } else {
                // No matches found - create clear background check entry directly
                let backgroundCheck = BackgroundCheck(
                    status: "clear",
                    matchType: "no_match",
                    reviewStatus: "approved",
                    reviewedBy: try! currentUser.requireID(),
                    requiresReview: false
                )
                
                member.addBackgroundCheck(backgroundCheck)
                
                return member.save(on: req.db).flatMap { _ in
                    return self.createTimelineItem(
                        req: req,
                        memberId: try! member.requireID(),
                        creatorId: try! currentUser.requireID(),
                        title: "Background Check Cleared",
                        description: "Background check completed with no matches found.",
                        status: "background_check_clear",
                        visible: true
                    ).map { _ in
                        return BackgroundCheckTriggerResponse(
                            success: true,
                            message: "Background check completed. No matches found.",
                            matchesFound: 0,
                            requiresReview: false,
                            queueItemId: nil
                        )
                    }
                }
            }
        }
    }
    
    // MARK: - Reject Background Check
    func rejectBackgroundCheck(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let queueIDString = req.parameters.get("queueID"),
              let queueID = UUID(uuidString: queueIDString) else {
            throw Abort(.badRequest, reason: "Invalid queue item ID")
        }
        
        let input = try req.content.decode(BackgroundCheckReviewInput.self)
        
        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                return BackgroundCheckReviewQueue.find(queueID, on: req.db).flatMap { queueItem in
                    guard let queueItem = queueItem else {
                        return req.eventLoop.makeFailedFuture(Abort(.notFound, reason: "Queue item not found")) as EventLoopFuture<HTTPStatus>
                    }
                    
                    guard queueItem.reviewStatus == .pending else {
                        return req.eventLoop.future(error: Abort(.conflict, reason: "Queue item has already been reviewed"))
                    }
                    
                    // Update queue item
                    queueItem.reviewStatus = .rejected
                    queueItem.$reviewedBy.id = try! currentUser.requireID()
                    queueItem.reviewedAt = Date()
                    queueItem.reviewNotes = input.notes
                    queueItem.selectedOffenderIndex = input.selectedOffenderIndex
                    queueItem.matchSelections = input.matchSelections
                    
                    return queueItem.save(on: req.db).flatMap { _ in
                        return queueItem.$member.load(on: req.db).flatMap { _ in
                            // Add clear background check to member's security profile
                            let backgroundCheck = BackgroundCheck(
                                status: "clear",
                                matchType: "false_positive",
                                reviewStatus: "rejected",
                                reviewedBy: try! currentUser.requireID(),
                                requiresReview: false
                            )
                            // Only process member-based background checks for now
                            guard let member = queueItem.member else {
                                return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Household background checks not yet supported in approval workflow"))
                            }

                            member.addBackgroundCheck(backgroundCheck)

                            return member.save(on: req.db).flatMap { _ in
                                // Create timeline item
                                return self.createTimelineItem(
                                    req: req,
                                    memberId: try! member.requireID(),
                                    creatorId: try! currentUser.requireID(),
                                    title: "Background Check Cleared",
                                    description: "Background check rejected by admin. No security concerns.",
                                    status: "background_check_rejected",
                                    visible: false
                                ).map { _ in
                                    return .ok
                                }
                            }
                        }
                    }
                }
            }
        }
    }       
    
    private func createTimelineItem(
        req: Request,
        memberId: UUID,
        creatorId: UUID,
        title: String,
        description: String,
        status: String,
        visible: Bool
    ) -> EventLoopFuture<Void> {
        let timelineItem = TimeLineItemMessage.general(
            memberId: memberId,
            title: title,
            desc: description,
            status: status,
            visible: visible,
            meta: nil
        ).toTimelineItem()
        
        return try! TimelineControllerController.create(
            [timelineItem],
            creatorId: creatorId,
            req: req
        ).transform(to: ())
    }
    
    private func applySecurityTags(
        req: Request,
        member: Member,
        queueItem: BackgroundCheckReviewQueue,
        currentUser: User
    ) -> EventLoopFuture<Void> {
        // Create security tags based on risk level
        var tagInputs: [TagInput] = [
            TagInput(
                name: "Offender Registry Flagged",
                key: "offender_registry_flagged",
                color: "FF6B6B" // Red color for security flags
            )
        ]
        
        if let riskLevel = queueItem.highestRiskLevel {
            if riskLevel.contains("III") {
                tagInputs.append(TagInput(
                    name: "Risk Level III",
                    key: "risk_level_3",
                    color: "DC2626" // Dark red for highest risk
                ))
            } else if riskLevel.contains("II") {
                tagInputs.append(TagInput(
                    name: "Risk Level II",
                    key: "risk_level_2",
                    color: "F59E0B" // Orange for medium risk
                ))
            } else if riskLevel.contains("I") {
                tagInputs.append(TagInput(
                    name: "Risk Level I",
                    key: "risk_level_1",
                    color: "FCD34D" // Yellow for low risk
                ))
            }
        }
        
        // Convert TagInputs to Tags
        let allTags = tagInputs.compactMap { $0.tag() }
        let tagNames = allTags.compactMap { $0.name }.joined(separator: ", ")
        
        // Create tags and associate with member
        return member.$tags.create(allTags, on: req.db).flatMap { _ in
            // Create timeline entry for audit trail
            let timelineItem = TimeLineItemMessage.generalMemberUpdate(
                member: member,
                title: "Security Tags Applied",
                desc: "Security tags applied based on background check results: \(tagNames)",
                status: "security_tags_applied"
            ).toTimelineItem()
            
            return try! TimelineControllerController.create(
                [timelineItem],
                creatorId: currentUser.id!,
                req: req
            ).transform(to: ())
        }
    }
}

// MARK: - Request/Response Models
struct BackgroundCheckTriggerInput: Content, Validatable {
    let fullName: String
    let zipCode: String

    static func validations(_ validations: inout Validations) {
        validations.add("fullName", as: String.self, is: !.empty)
        validations.add("zipCode", as: String.self, is: .count(5...5))
    }
}

struct BackgroundCheckTriggerResponse: Content {
    let success: Bool
    let message: String
    let matchesFound: Int
    let requiresReview: Bool
    let queueItemId: UUID?
}

struct BackgroundCheckReviewInput: Content {
    let notes: String?
    let selectedOffenderIndex: Int?
    let matchSelections: [MatchSelection]?
}

struct BackgroundCheckQueueItem: Content {
    let id: UUID
    let member: MemberSummary?
    let household: HouseholdSummary?
    let targetType: BackgroundCheckTargetType
    let triggeredBy: UserSummary
    let fullName: String
    let zipCode: String
    let matchedCount: Int
    let highestRiskLevel: String?
    let createdAt: Date
    let rawResponse: ZylaOffenderResponse
}

struct MemberSummary: Content {
    let id: UUID
    let firstName: String
    let lastName: String
    let email: String
}

struct HouseholdSummary: Content {
    let id: UUID
    let name: String
    let memberCount: Int
}

struct UserSummary: Content {
    let id: UUID
    let firstName: String
    let lastName: String
    let email: String
}

// MARK: - Location Search Structures
struct LocationSearchInput: Content {
    let memberId: UUID?
    let householdId: UUID?
    let address: String
    let latitude: Double
    let longitude: Double
}

struct LocationSearchResponse: Content {
    let queueId: UUID
    let exactMatches: [ZylaOffenderRecord]
    let nearbyOffenders: [ZylaOffenderRecord]
    let totalCount: Int
    let searchQuery: ZylaLocationQuery
}

// MARK: - Household Background Check Models
struct HouseholdBackgroundCheckInput: Content, Validatable {
    let zipCode: String

    static func validations(_ validations: inout Validations) {
        validations.add("zipCode", as: String.self, is: .count(5...5))
    }
}

struct HouseholdBackgroundCheckResponse: Content {
    let success: Bool
    let message: String
    let householdId: UUID
    let totalMembers: Int
    let checkedMembers: Int
    let totalMatches: Int
    let requiresReview: Bool
    let queueItemIds: [UUID]
    let memberResults: [MemberBackgroundCheckResult]
}

struct MemberBackgroundCheckResult: Content {
    let memberId: UUID
    let memberName: String
    let matchesFound: Int
    let requiresReview: Bool
    let queueItemId: UUID?
    let success: Bool
    let error: String?
}
