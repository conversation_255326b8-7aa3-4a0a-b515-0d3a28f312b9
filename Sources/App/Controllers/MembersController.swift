
//
//  File.swift
//
//
//  Created by <PERSON> on 1/31/23.
//

import Foundation
import Vapor
import Fluent

enum MemberSortField: String, Codable {
    case firstName
    case dob
    case status
    case score
    case gender
    case sexualIdentity
    case genderIdentity
    case military
    case pronouns
    case email
    case referredBy
    case ethnicity
    case lastContact
    case lang
    case createdAt
    case updatedAt
}

enum SortDirection: String, Codable {
    case asc
    case desc
}

struct MemberQuery: Content {
    var sortBy: MemberSortField? = .createdAt
    var sortDirection: SortDirection? = .asc
    var firstName: String?
    var lastName: String?
    var email: String?
    var status: String?
    var tags: [String]?
    var search: String?
}

struct ScheduleResponse: Content {
    var today: [CarePackageItem]
    var pending: [CarePackageItem]
    var booked: [CarePackageItem]
}

struct Dashboard: Content {
    var member:Member
    var survey:[Survey]
}

//https://share.hsforms.com/****************************
struct MembersController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("members")
        
        users.get(use: index)
        users.get(":memberID",                        use: lookup)
        users.get("all",                              use: fetchAllMembers)
        users.get("team",                             use: fetchAllMembersForTeam)
        users.get("find",                             use: searchMembers)
        users.get([":memberID", "attachments"],       use: attachments)
        users.get([":memberID", "insurances"],        use: insurances)
        users.get([":memberID", "notes"],             use: memberNotes)
        users.get([":memberID", "carepackages"],      use: carepackages)
        users.get([":memberID", "dashboard"],         use: dashboard)
        users.get([":memberID", "conversations"],     use: conversations)
        users.get([":memberID", "consents"],          use: consents)
        users.get([":memberID", "teams"],             use: memberTeams)
        users.get([":memberID", "chats"],             use: chats)
        users.get([":memberID", "schedule"],          use: schedule)
        
        users.post(use: create)
        users.post([":memberID", "attachments"],      use: createAttachment)

        users.put(":memberID",                        use: update)
        users.delete(":memberID",                     use: delete)

        //primary user management
        users.put([":memberID", "primaryUser"],       use: setPrimaryUser)
        users.delete([":memberID", "primaryUser"],    use: removePrimaryUser)

        //member status
        users.get([":memberID", "memberStatus"],        use: fetchMemberStatus)
        users.post([":memberID", "memberStatus"],       use: createMemberStatus)
        users.put([":memberID", "memberStatus"],        use: updateMemberStatus)
        users.delete([":memberID", "status", ":statusId"],use: deleteStatus)
    }
    
    //MARK - Fetch
    func lookup(req: Request) throws -> EventLoopFuture<Member> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        return try findMember(id: id, req: req)
    }
    
    func findMember(id:String, req:Request) throws -> EventLoopFuture<Member> {
        guard let uuid = UUID(id) else { throw NetworkError.error(type: .household)}
        return try findMemberBy(id: uuid, req: req)
    }
    
    func findMemberStatus(id:String, req:Request) throws -> EventLoopFuture<MemberStatus> {
        guard let uuid = UUID(id) else { throw NetworkError.error(type: .status)}
        return MemberStatus.query(on: req.db)
            .filter(\.$id == uuid)
            .first().flatMapThrowing { model in
                guard let foundModel = model else { throw NetworkError.error(type: .status) }
                return foundModel
            }
    }
    
    static func findAll(req: Request, ids:[String]) throws -> EventLoopFuture<[Member]> {
        let allids = ids.compactMap({ UUID(uuidString: $0)})
        return Member.query(on: req.db)
            .filter(\.$id ~~ allids)
            .with(\.$tags)
            .with(\.$attachments)
            .all()
    }
    
    func findMemberBy(id:UUID, req:Request) throws -> EventLoopFuture<Member> {
        return try self.buildQuery(query: Member.query(on: req.db), req: req)
            .filter(\.$id == id)
            .with(\.$attachments)
            .with(\.$schools)
            .with(\.$households)
            .with(\.$address)
            .with(\.$headOfHouse)
            .with(\.$notes)
            .with(\.$phones)
            .with(\.$tags)
            .with(\.$problems)
            .with(\.$primaryUser)
            .first().flatMapThrowing { model in
                guard let foundModel = model else { throw NetworkError.error(type: .member) }
                return foundModel
            }
    }
    
    func schedule(req: Request) throws -> EventLoopFuture<ScheduleResponse> {
        guard let memberId = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        var response = ScheduleResponse(today: [], pending: [], booked: [])
        return try! fetchMembersAppointments(req: req, memberId: memberId).flatMap { appointments in
            return try! fetchMembersItems(req: req, memberId: memberId).flatMap({ items in
                
                let todayEpoch = Date()
                let startOfDay = Int(todayEpoch.startOfDay.timeIntervalSince1970)
                let endOfDay = Int(todayEpoch.endOfDay.timeIntervalSince1970)
                
                for item in items {
                    if item.status == "pending" {
                        response.pending.append(item)
                    } else if item.status == "booked" {
                        item.appointments.forEach { appointment in
                            if (appointment.scheduleEpoc ?? 0) >= startOfDay && (appointment.scheduleEpoc ?? 0) <= endOfDay {
                                response.today.append(item)
                            } else if (appointment.scheduleEpoc ?? 0) >= startOfDay  {
                                response.booked.append(item)
                            }
                        }
                    }
                }
                return req.eventLoop.future(response)
            })
        }
    }
    
    fileprivate func fetchMembersItems(req: Request, memberId: String) throws -> EventLoopFuture<[CarePackageItem]> {
        return CarePackage.query(on: req.db)
            .filter(\.$reciever == memberId)
            .filter(\.$status == "active")
            .with(\.$items)
            .all().flatMap { cp in
                let carePlanIDs = cp.compactMap({$0.id})
                return CarePackageItem.query(on: req.db)
                    .join(parent: \.$carepackage)
                    .filter(CarePackage.self, \.$id ~~ carePlanIDs)
                    .filter(\.$status != "complete")
                    .with(\.$carepackage)
                    .with(\.$network, { network in
                        network.with(\.$services)
                        network.with(\.$address)
                        network.with(\.$phones)
                    })
                    .with(\.$appointments)
                    .all()
            }
    }
    
    
    fileprivate func fetchMembersAppointments(req: Request, memberId: String) throws -> EventLoopFuture<[Appointment]> {
        return Appointment.query(on: req.db)
            .filter(\.$memberID == memberId.lowercased())
            .filter(\.$scheduleEpoc, .greaterThanOrEqual,  Int(Date().startOfDay.timeIntervalSince1970))
            .filter(\.$status == "booked").all()
    }
    
    func fetchAllMembersForTeam(req: Request) throws -> EventLoopFuture<Page<Member>> {
        guard let teamID: String = req.query["team"] else { throw NetworkError.error(type: .badRequest, msg: "Team ID is required.") }
        let org:String? = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return try self.allMembers(req: req, teamID: teamID, orgID: orgID)
    }
    
    func fetchAllMembers(req: Request) throws -> EventLoopFuture<Page<Member>> {
        let org:String? = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return try self.allHouseholdMembers(req: req, orgID: orgID)
    }
    
    func searchMembers(req: Request) throws -> EventLoopFuture<Page<Member>> {
        guard let orgID:String = req.query["org"] else { throw NetworkError.error(type: .member) }
        return try self.searchMembers(req: req, orgID: orgID)
    }

    // MARK: - Member Teams (Optimized)
    func memberTeams(req: Request) throws -> EventLoopFuture<[Team]> {
        guard let memberIDString = req.parameters.get("memberID"),
              let memberID = UUID(memberIDString) else {
            throw NetworkError.error(type: .member)
        }

        // Single optimized query using joins - most efficient approach
        return Team.query(on: req.db)
            .join(HouseholdTeams.self, on: \Team.$id == \HouseholdTeams.$team.$id)
            .join(HouseholdMembers.self, on: \HouseholdTeams.$household.$id == \HouseholdMembers.$household.$id)
            .filter(HouseholdMembers.self, \.$member.$id == memberID)            
            .with(\.$navigators, { nav in
                nav.with(\.$attachments)
            })
            .unique()  // Critical for performance - removes duplicates at DB level
            .all()
    }
    
    
    func dashboard(req: Request) throws -> EventLoopFuture<Dashboard> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        return try buildDashboard(req: req, id: id)
    }
    
    func consents(req: Request) throws -> EventLoopFuture<Page<Consent>> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        guard let uuid = UUID(uuidString: id)  else { throw NetworkError.error(type: .member) }
        return Consent.query(on: req.db).filter(\.$memberId == uuid).sort(\.$createdAt, .descending).paginate(for: req)
    }
    
    
    func buildDashboard(req:Request, id:String) throws -> EventLoopFuture<Dashboard> {
        return try self.findMember(id: id, req: req).flatMap { member in
            return try! SurveysController.findLatestSurvey(req: req, memberID: id).flatMap { surveys in
                if surveys.isEmpty {
                    return try! SurveysController.findLatestSurvey(req: req, memberID: id, useLast: false).flatMap { surveys in
                        return req.eventLoop.future(Dashboard(member: member, survey: surveys))
                    }
                } else {
                    return req.eventLoop.future(Dashboard(member: member, survey: surveys))
                }
            }
        }
    }
    
    func chats(req: Request) throws -> EventLoopFuture<Page<MemberChat>> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        return try! MemberChatsController.chatsForMember(creatorID: id, req: req)
    }
    
    func conversations(req: Request) throws -> EventLoopFuture<ClientResponse> {
        return try! TwilioController().getMembersConvo(req: req)
    }
    
    func carepackages(req: Request) throws -> EventLoopFuture<Page<CarePackage>> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        return CarePackage.query(on: req.db)
            .filter(\.$reciever == id)
            .with(\.$items) { item in
                item.with(\.$items) { packageItem in
                    packageItem.with(\.$services)
                }
            }
            .with(\.$reason)
            .sort(\.$updatedAt, .descending)
            .paginate(for: req)
    }
    
    func insurances(req: Request) throws -> EventLoopFuture<Page<InsurancePolicy>> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        guard let uuid = UUID(uuidString: id)  else { throw NetworkError.error(type: .member) }
        return try! InsuranceController().findMemberPolicyBy(id: uuid, req: req)
    }
    
    func attachments(req: Request) throws -> EventLoopFuture<Page<Attachment>> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        let kind:String?            = req.query["kind"]
        guard let uuid = UUID(uuidString:id) else { throw NetworkError.error(type: .member) }
        let query = Attachment.query(on: req.db)
        query.join(parent: \.$member)
            .filter(Member.self, \.$id == uuid)
        
        if let knd = kind {
            query.filter(\.$kind == knd)
        }
        
        return query.sort(\.$createdAt, .descending).paginate(for: req)
    }
    
    func memberNotes(req: Request) throws -> EventLoopFuture<Page<Note>> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        guard let uuid = UUID(uuidString:id) else { throw NetworkError.error(type: .member) }
        let search:String?            = req.query["search"]
        let status:String?            = req.query["status"]
        let query = Note.query(on: req.db)
        query.join(parent: \.$member)
            .filter(Member.self, \.$id == uuid)
        
            .with(\.$attachments)
            .with(\.$member)
        //            .with(\.$creator)
            .with(\.$creator, { user in
                user.with(\.$attachments)
            })
            .with(\.$tags)
        if let srch = search, !srch.isEmpty {
            query.filter(\.$msg, .custom("ilike"), "%\(srch.lowercased())%")
        }
        
        if let status, !status.isEmpty {
            query.filter(\.$status == status)
        }
        
        return query.sort(\.$createdAt, .descending).paginate(for: req)
    }
    
    static func membersHousehold(req: Request, id:String) throws -> EventLoopFuture<[Household]> {
        return Member.query(on: req.db).filter(\.$id == UUID(uuidString: id)!).with(\.$households).first().flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .member) }
            return foundModel.households
        }
    }
    
    func find(req: Request) throws -> EventLoopFuture<Member> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        return try MembersController.find(req: req, id: id)
    }
    
    static func find(req: Request, id:String) throws -> EventLoopFuture<Member> {
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .member)}
        return Member.query(on: req.db).filter(\.$id == networkID)
            .with(\.$attachments)
            .with(\.$schools)
            .with(\.$households)
            .with(\.$address)
            .with(\.$headOfHouse)
            .with(\.$notes)
            .with(\.$phones)
            .with(\.$tags)
            .with(\.$memberStatus)
            .with(\.$problems)
            .with(\.$primaryUser)
            .first().flatMapThrowing() { model in
                guard let foundModel = model else { throw NetworkError.error(type: .member) }
                return foundModel
            }
    }
    
    static func findMembers(req: Request, ids:[String]) throws -> EventLoopFuture<[Member]> {
        guard !ids.isEmpty else { throw NetworkError.error(type: .member)}
        let allIDS = ids.compactMap({UUID($0)})
        return Member.query(on: req.db).filter(\.$id ~~ allIDS).all()
    }
    
    static func findTaskMembers(req: Request, ids:[String]) throws -> EventLoopFuture<[Member]> {
        guard !ids.isEmpty else { return req.eventLoop.future([]) }
        let allIDS = ids.compactMap({UUID($0)})
        return Member.query(on: req.db).filter(\.$id ~~ allIDS).all()
    }
    
    func orgMembers(req: Request, orgID:String) throws -> EventLoopFuture<Page<Member>> {
        guard let org = UUID(uuidString: orgID) else { throw NetworkError.error(type: .organization) }
        return try buildQuery(query: Member.query(on: req.db), req: req)
            .with(\.$attachments)
            .with(\.$schools)
            .with(\.$tags)
            .with(\.$primaryUser)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == org)
            .paginate(for: req)
    }
    
    func allMembers(req: Request, teamID: String, orgID:String) throws -> EventLoopFuture<Page<Member>> {
        return try TeamsController.queryTeamWithHouseholds(req: req, id: teamID).flatMap { team in
            let allids = team.households.compactMap({ $0.id})
            return HouseholdMembers.query(on: req.db)
                .filter(\.$household.$id ~~ allids)
                .unique()
                .all(\.$member.$id).flatMap { memberIds in
                    return try! buildQuery(query: Member.query(on: req.db), req: req)
                        .with(\.$attachments)
                        .with(\.$address)
                        .with(\.$phones)
                        .with(\.$primaryUser)
                        .filter(\.$id ~~ memberIds)  // Filter Members by the unique IDs
                        .sort(\.$updatedAt, .descending)
                        .paginate(for: req)
                }
        }
    }
    
    func allHouseholdMembers(req: Request, orgID:String) throws -> EventLoopFuture<Page<Member>> {
        return try buildQuery(query: Member.query(on: req.db), req: req)
            .with(\.$attachments)
            .with(\.$address)
            .with(\.$tags)
            .with(\.$schools)
            .with(\.$phones)
            .with(\.$households)
            .with(\.$primaryUser)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(uuidString: orgID)!)
            .paginate(for: req)
    }
    
    func searchMembers(req: Request, orgID: String) throws -> EventLoopFuture<Page<Member>> {
        return try buildQuery(query: Member.query(on: req.db), req: req)
            .with(\.$attachments)
            .with(\.$address)
            .with(\.$tags)
            .with(\.$schools)
            .with(\.$phones)
            .with(\.$households)
            .with(\.$primaryUser)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(uuidString: orgID)!)
            .paginate(for: req)
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Member]> {
        let org:String?            = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return try self.buildQuery(query: Member.query(on: req.db), req: req)
            .with(\.$schools)
            .with(\.$notes)
            .with(\.$phones)
            .with(\.$tags)
            .with(\.$households)
            .with(\.$households, { household in
                household.with(\.$teams)
            })
            .with(\.$attachments)
            .with(\.$primaryUser)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(uuidString: orgID)!)
            .all()
    }
    
    
    //MARK: - Create
    static func createWith(req: Request, authUser:AuthUser, signup:MemberSignup, org:Organization) throws -> EventLoopFuture<Void> {
        guard let authUser = try? authUser.asPublic() else {
            return req.eventLoop.future(error: Abort(.internalServerError))
        }
        let member = signup.member(auth: authUser.id.uuidString)
        return org.$members.create(member, on: req.db)
    }
    
    func create(req: Request) throws -> EventLoopFuture<Member> {
        var input = try req.content.decode(MembersCreateInput.self)
        input = input.cleanUp()
        let member = input.member()
        if let enrolledOn = input.enrolledOn, !enrolledOn.isEmpty,
           let date = Date.yearMonthDayDateWithDashFromString(dateString: enrolledOn) {
            member.enrolledOn = Date.yearMonthDayDateWithDashFromDate(date: date)
        }
        
        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in

            // Set primary user if provided
            if let primaryUserIDString = input.primaryUserID,
               let primaryUserID = UUID(uuidString: primaryUserIDString) {
                member.$primaryUser.id = primaryUserID
            }

            return org.$members.create(member, on: req.db).transform(to: member).flatMap { orgMember in
                
                return try! MembersController.find(req: req, id: orgMember.id?.uuidString ?? "").flatMap({ foundMember in
                    return try! updateTagsIfNeeded(req: req, tags: input.allTags, member: foundMember).flatMap { _ in
                        return try! createPhoneIfNeeded(req: req, phone: input.phone, member: member).flatMap({ member in
                            
                            return try! createSchoolIfNeeded(req: req, member: member, input: input).flatMap({ member in
                                
                                return try! createMemberStatus(req: req, member: member, input: input).flatMap({ memberStatus in
                                    
                                    return try! AuthController.userFromToken(req: req).flatMap { user in
                                        
                                        return try! createTimelineItemIfNeeded(req: req, member: member, input: input, creator: user, memberStatus: memberStatus)
                                        
                                    }
                                })
                            })
                        })
                    }
                })
            }
        }
    }
    
    fileprivate func fetchPregnacyMemberStatus(req:Request, member:Member) throws -> EventLoopFuture<MemberStatus?> {
        return MemberStatus.query(on: req.db)
            .filter(\.$member.$id == member.id)
            .filter(\.$kind == "pregnancy")
            .first()
    }
    
    fileprivate func createMemberStatus(req:Request, member:Member, input:MembersCreateInput? = nil, updateInput: MembersUpdateInput? = nil) throws -> EventLoopFuture<MemberStatus?> {
        let memberStatus: MemberStatusInput? = input?.memberStatus ?? updateInput?.memberStatus
        let pregnancy: String? = input?.pregnancyStatus ?? updateInput?.pregnancyStatus
        
        if memberStatus?.kind == "pregnancy" {
            return try fetchPregnacyMemberStatus(req: req, member: member).flatMap { pregnacyMemberStatus in
                //we only want to store one reference of pregnacy
                if pregnacyMemberStatus != nil {
                    return req.eventLoop.future(nil)
                } else {
                    
                    return try! createMemberStatusIfNeeded(req: req, memberStatus: memberStatus, member: member)
                }
            }
        }
        else if let pregnancy,
                !pregnancy.isEmpty,
                pregnancy  == "pregnancy" || pregnancy  == "pregnant",
                let dueDate = member.deliveryDate {
            let memberStatusInput = MemberStatusInput(name: "pregnant", state: "pregnant", kind: "pregnancy", startDate: dueDate)
            
            return try fetchPregnacyMemberStatus(req: req, member: member).flatMap { pregnacyMemberStatus in
                
                if pregnacyMemberStatus?.name == memberStatusInput.name && pregnacyMemberStatus?.state == memberStatusInput.state && pregnacyMemberStatus?.kind == memberStatusInput.kind && pregnacyMemberStatus?.startDate == pregnacyMemberStatus?.startDate {
                    return req.eventLoop.future(nil)
                } else {
                    return try! createMemberStatusIfNeeded(req: req, memberStatus: memberStatusInput, member: member)
                }
            }
        }
        else {
            return try! createMemberStatusIfNeeded(req: req, memberStatus: memberStatus, member: member)
        }
    }
    fileprivate func createMemberStatusIfNeeded(req: Request, memberStatus: MemberStatusInput?, member: Member) throws -> EventLoopFuture<MemberStatus?> {
        if let statusInput = memberStatus {
            if let id = statusInput.id {
                return MemberStatus.query(on: req.db)
                    .filter(\.$id == UUID(id)!)
                    .with(\.$address)
                    .first()
                    .flatMap { member in
                        if let member {
                            let data = statusInput.returnUpdatedModel(status: member)
                            return data.update(on: req.db).transform(to: data).flatMap { status in
                                return try! self.updateMemberAddressIfNeeded(req: req, addressInput: statusInput.address, status: status).transform(to: status)
                            }
                        } else {
                            return req.eventLoop.future(nil)
                        }
                    }
            } else {
                let status = MemberStatus(name: statusInput.name, state: statusInput.state, kind: statusInput.kind, location: statusInput.location, startDate: statusInput.startDate, endDate: statusInput.endDate)
                return member.$memberStatus.create(status, on: req.db).transform(to: status).flatMap { memberStatus in
                    if let addressInput = statusInput.address {
                        return memberStatus.$address.create(addressInput.address(), on: req.db).transform(to: memberStatus)
                    } else {
                        return req.eventLoop.future(memberStatus)
                    }
                }
            }
        } else {
            return req.eventLoop.future(nil)
        }
    }
    
    fileprivate func updateMemberAddressIfNeeded(req:Request, addressInput: AddressInput?, status: MemberStatus) throws -> EventLoopFuture<MemberStatus> {
        if let addressInput {
            return try AddressService.updateAddressIfNeeded(req: req, input: addressInput, model: status).transform(to: status)
        } else {
            return req.eventLoop.future(status)
        }
    }
    
    fileprivate func createSchoolIfNeeded(req:Request, member:Member, input:MembersCreateInput) throws -> EventLoopFuture<Member> {
        if input.hasSchool(), let schoolInput = input.school {
            let school = School(name: schoolInput.name, grade: schoolInput.grade)
            return member.$schools.create(school, on: req.db).transform(to: member)
        } else {
            return req.eventLoop.future(member)
        }
    }
    
    fileprivate func createPhoneIfNeeded(req:Request, phone:String?, member:Member) throws -> EventLoopFuture<Member> {
        if let phone = phone {
            let phoneNumber = PhoneNumber(label: "main", number: phone)
            return member.$phones.create(phoneNumber, on: req.db).transform(to: member)
        } else {
            return req.eventLoop.future(member)
        }
    }
    
    func createAttachment(req: Request) throws -> EventLoopFuture<Member> {
        let input = try req.content.decode(UploadInput.self)
        return try! find(req: req).flatMap { member in
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let type = resposne.isPDF() ? "pdf" : "image"
                if input.isConsent ?? false {
                    let consent = Consent(name: input.name,
                                          memberId: member.id,
                                          type: type,
                                          url: resposne.secure_url)
                    return consent.create(on: req.db).transform(to: consent).flatMap { updateConsent in
                        return try! consentTimelineitem(req: req, member, input.name, consentId: updateConsent.id)
                    }
                    
                } else {
                    let attach = Attachment(name: input.name,
                                            kind: input.type,
                                            type: type,
                                            url: isProfile(type: input.type) ? resposne.profileURL() : resposne.secure_url,
                                            category: input.category,
                                            refID: resposne.public_id)
                    return member.$attachments.create(attach, on: req.db).transform(to: member)
                }
            }
        }
    }
    
    
    //MARK: - Update
    func update(req: Request) throws -> EventLoopFuture<Member> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        let input = try req.content.decode(MembersUpdateInput.self)
        let inputMemberStatus = input.status ?? ""
        return try AuthController.userFromToken(req: req).flatMap { authUser in
            
            return try! MembersController.find(req: req, id: id).flatMap { foundMember in
                
                return try! createStatusTimeLine(req: req, foundMember: foundMember, authUser: authUser, inputMemberStatus: inputMemberStatus).flatMap({ _ in
                    
                    let msgChange = timelineMessage(member: foundMember, input: input)
                    let user = input.returnUpdatedModel(user: foundMember)

                    // Update primary user if provided
                    if let primaryUserIDString = input.primaryUserID {
                        if primaryUserIDString.isEmpty {
                            user.$primaryUser.id = nil
                        } else if let primaryUserID = UUID(uuidString: primaryUserIDString) {
                            user.$primaryUser.id = primaryUserID
                        }
                    }

                    return user.update(on: req.db).transform(to: user).flatMap { member in
                        
                        return try! updateTagsIfNeeded(req: req, tags: input.allTags, member: member).flatMap { _ in
                            
                            return try! updatePhoneIfNeeded(req: req, input: input, member: member).flatMap { _ in
                                
                                return try! updateAddressIfNeeded(req: req, input: input, member: member).flatMap({ _ in
                                    
                                    return try! createReasonIfNeeded(req: req, input: input.reason, member: member).flatMap({ member in
                                        
                                        return try! createMemberStatus(req: req, member: member, updateInput: input).flatMap({ memberStatus in
                                            
                                            if !inputMemberStatus.isEmpty, foundMember.status != inputMemberStatus, let creatorId = authUser.id {
                                                
                                                return try! TimelineControllerController.create([
                                                    TimeLineItemMessage.generalMemberUpdate(
                                                        member: foundMember,
                                                        title: "Status Changed",
                                                        desc: "Status changed from \(foundMember.status ?? "") to \(inputMemberStatus)",
                                                        status: "status",
                                                        visible: true,
                                                        meta: nil).toTimelineItem(),
                                                ], creatorId: creatorId, req: req).flatMap({ _ in
                                                    return try! createTimelineItemIfNeeded(req: req, member: foundMember, input: input, creator: authUser, messageChange: msgChange, memberStatus: memberStatus)
                                                })
                                            } else {
                                                return try! createTimelineItemIfNeeded(req: req, member: foundMember, input: input, creator: authUser, messageChange: msgChange, memberStatus: memberStatus)
                                            }
                                        })
                                    })
                                })
                            }
                        }
                    }
                })
            }
        }
    }
    
    fileprivate func createStatusTimeLine(req: Request,
                                          foundMember: Member,
                                          authUser: User,
                                          inputMemberStatus: String) throws -> EventLoopFuture<Void> {
        if !inputMemberStatus.isEmpty, foundMember.status != inputMemberStatus, let creatorId = authUser.id {
            let fromStatus = foundMember.status ?? ""
            let toStatus = inputMemberStatus
            
            return try! TimelineControllerController.create([
                TimeLineItemMessage.generalMemberUpdate(
                    member: foundMember,
                    title: "Member Status Updated to \(toStatus)",
                    desc: "Member status changed from \(fromStatus) to \(toStatus)",
                    status: "status",
                    visible: true,
                    meta: nil).toTimelineItem(),
            ], creatorId: creatorId, req: req)
        } else {
            return req.eventLoop.future()
        }
    }
    
    fileprivate func timelineMessage(member: Member, input: MembersUpdateInput) -> TimelineMessageChange {
        let status = member.memberStatus.last?.kind ?? "-"
        let inputStatus = input.memberStatus?.kind ?? "empty"
        let pregnancyStatus = member.pregnacyStatusChanged(status: input.pregnancyStatus ?? "")
        print("\(status) == \(inputStatus) | \(status == inputStatus)")
        print(member.memberStatus.count)
        if inputStatus == "homeless" {
            let homelessStatus = status == inputStatus
            return TimelineMessageChange(homelessChange: homelessStatus,
                                         pregnacyChange: pregnancyStatus)
            
        } else if inputStatus == "admission" {
            return TimelineMessageChange(homelessChange: false, pregnacyChange: pregnancyStatus)
        } else {
            return TimelineMessageChange(homelessChange: false, pregnacyChange: pregnancyStatus)
        }
    }
    
    static func updateLastContact(req:Request, member: Member) throws -> EventLoopFuture<Member> {
        member.lastContact = Date.yearMonthDayString(date: Date())
        return member.update(on: req.db).transform(to: member)
    }
    
    fileprivate func createTimelineItemIfNeeded(req: Request, member: Member, input: MembersCreateInput, creator: User?, memberStatus: MemberStatus?) throws -> EventLoopFuture<Member> {
        var items:[TimelineItem] = []
        
        let createItem = TimeLineItemMessage.generalMemberUpdate(member: member,
                                                                 title: "Member Created",
                                                                 desc: "Member has been created on \(DateFormatter.taskDueFullDate(date: Date()))",
                                                                 status: input.status ?? "active").toTimelineItem()
        items.append(createItem)
        
        if let enrolledOn = input.enrolledOn, !enrolledOn.isEmpty,
           let date = Date.yearMonthDayDateWithDashFromString(dateString: enrolledOn) {
            let formattedEnrolled = DateFormatter.taskDueFullDate(date: date)
            let createItem = TimeLineItemMessage.generalMemberUpdate(member: member,
                                                                     title: "Member Enrolled In Program",
                                                                     desc: "Member has been enrolled with an enrollment date of \(formattedEnrolled)",
                                                                     status: input.status ?? "active").toTimelineItem()
            items.append(createItem)
        }
        
        if let memberStatus {
            
            if memberStatus.kind == "homeless" {
                
                let title = memberStatus.isStarted() ? "Member is homeless" : "Member no longer homeless"
                var desc = "Member has been flagged as \(memberStatus.isStarted() ? "homeless" : "no longer homeless")"
                if let location = memberStatus.location, !location.isEmpty {
                    desc.append(" and currently staying at \(location).")
                } else if memberStatus.state?.lowercased() == "mobile_living" {
                    desc.append(" and currently living in vehicle.")
                } else {
                    desc.append(" and currently living in the streets.")
                }
                
                let createItem = TimeLineItemMessage.generalMemberUpdate(member: member,
                                                                         title: title,
                                                                         desc: desc,
                                                                         status: memberStatus.state ?? "").toTimelineItem()
                
                items.append(createItem)
                
                
            } else if memberStatus.kind == "admission" {
                var json: [String : String] = [:]
                json["admissionDate"] = memberStatus.startDate ?? ""
                json["dischargeDate"] = memberStatus.endDate ?? ""
                let title = memberStatus.state?.lowercased() == "admitted" ? "Admitted to Hospital" : "Discharged from Hospital"
                let desc = "Member has been \(memberStatus.state?.lowercased() ?? "admitted") to \(memberStatus.location ?? "hospital") \(memberStatus.address.last?.fullAddress() ?? "")"
                
                let createItem = TimeLineItemMessage.generalMemberUpdate(member: member,
                                                                         title: title,
                                                                         desc: desc,
                                                                         status: memberStatus.state ?? "",
                                                                         meta: .init(data: json)).toTimelineItem()
                
                items.append(createItem)
                
            } else if memberStatus.kind == "pregnancy" {
                
            }
        }
        
        
        if items.isEmpty {
            
            return req.eventLoop.future(member)
            
        } else {
            return items.create(on: req.db).transform(to: items).flatMap { savedItems in
                return savedItems.sequencedFlatMapEach(on: req.eventLoop) { updatedTimeLineItem in
                    if let id = creator?.id {
                        updatedTimeLineItem.$creator.id = id
                    }
                    return updatedTimeLineItem.update(on: req.db).eventLoop.future(member)
                }.flatMap { _ in
                    return req.eventLoop.future(member)
                }
            }
        }
    }
    fileprivate func filterTimelines(req:Request, memberId: UUID, itemsToCreate: [TimelineItem]) throws -> EventLoopFuture<[TimelineItem]> {
        let startOfDay = Calendar.current.startOfDay(for: Date())
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        return TimelineItem.query(on: req.db)
            .filter(\.$memberId == memberId)
            .filter(\.$createdAt >= startOfDay)
            .filter(\.$createdAt < endOfDay)
            .all().flatMap { fetchedItems in
                let items =  itemsToCreate.filter { item1 in
                    !fetchedItems.contains { item2 in
                        item1.status == item2.status && item1.desc == item2.desc
                    }
                }
                return req.eventLoop.future(items)
            }
    }
    
    fileprivate func createTimelineItemIfNeeded(req: Request, member: Member, input: MembersUpdateInput, creator: User?, messageChange: TimelineMessageChange, memberStatus: MemberStatus?) throws -> EventLoopFuture<Member> {
        var items:[TimelineItem] = []
        var isNewlyPregnant: Bool = false
        if messageChange.homelessChange, let homeless = input.homeless {
            let item = TimeLineItemMessage.homeless(member: member, isHomeless: homeless).toTimelineItem()
            items.append(item)
        }
        if messageChange.pregnacyChange, let status = member.pregnancyStatus {
            var baseString = ""
            if status.lowercased().contains("not_pregnant") {
                baseString.append("Member has been flagged has not pregnant")
            } else if status.lowercased() == "pregnant",
                      let deliveryDate = input.deliveryDate,
                      let date = DateFormatter.dateFromMultipleFormats(fromString: deliveryDate) {
                isNewlyPregnant = true
                baseString.append("Member has been flagged has pregnant with a expected delivery date set to \(DateFormatter.taskDueFullDate(date: date)).")
            } else if status.lowercased().contains("delivered"),
                      let deliveryDate = input.deliveryDate,
                      let date = DateFormatter.dateFromMultipleFormats(fromString: deliveryDate) {
                baseString.append("Member's pregnancy has been updated with a scheduled delivery date of \(DateFormatter.taskDueFullDate(date: date)).")
            } else if status.lowercased().contains("postpartum") {
                baseString.append("Member has been flagged has postpartum")
                
            } else if status.lowercased().contains("miscarriage"),
                      let deliveryDate = input.deliveryDate,
                      let date = DateFormatter.dateFromMultipleFormats(fromString: deliveryDate) {
                baseString.append("Member's pregnancy has been updated to reflect a miscarriage, with the miscarriage date set to \(DateFormatter.taskDueFullDate(date: date)).")
            }
            
            if !baseString.isEmpty {
                let item = TimeLineItemMessage.generalMemberUpdate(member: member,
                                                                   title: "Pregnancy Program",
                                                                   desc: baseString,
                                                                   status: status).toTimelineItem()
                items.append(item)
            }
        }
        
        if let memberStatus {
            
            let isStarted = memberStatus.isStarted()
            
            if memberStatus.kind == "homeless" {
                let title = isStarted ? "Member is homeless" : "Member no longer homeless"
                var desc = "Member has been flagged as \(memberStatus.isStarted() ? "homeless" : "no longer homeless")"
                if let location = memberStatus.location, !location.isEmpty {
                    desc.append(" and currently staying at \(location).")
                } else if memberStatus.state?.lowercased() == "mobile_living" {
                    desc.append(isStarted ? " and currently living in vehicle." : " and no longer living in vehicle.")
                } else {
                    desc.append(isStarted ? " and currently living in the streets." : " and no longer living in the streets.")
                }
                
                let createItem = TimeLineItemMessage.generalMemberUpdate(member: member,
                                                                         title: title,
                                                                         desc: desc,
                                                                         status: memberStatus.state ?? "").toTimelineItem()
                
                items.append(createItem)
                
                
            } else if memberStatus.kind == "admission" {
                var json: [String : String] = [:]
                json["admissionDate"] = memberStatus.startDate ?? ""
                json["dischargeDate"] = memberStatus.endDate ?? ""
                let address = input.memberStatus?.address?.address().fullAddress()
                let title = memberStatus.state?.lowercased() == "admitted" ? "Admitted to Hospital" : "Discharged from Hospital"
                let desc = "Member has been \(memberStatus.state?.lowercased() ?? "admitted") to \(memberStatus.location ?? "hospital") \(address ?? "")"
                
                let createItem = TimeLineItemMessage.generalMemberUpdate(member: member,
                                                                         title: title,
                                                                         desc: desc,
                                                                         status: memberStatus.state ?? "",
                                                                         meta: .init(data: json)).toTimelineItem()
                
                items.append(createItem)
                
            } else if memberStatus.kind == "pregnancy" {
                
            }
        }
        
        if items.isEmpty {
            
            return req.eventLoop.future(member)
            
        } else {
            return try filterTimelines(req: req, memberId: member.id!, itemsToCreate: items).flatMap { newItems in
                return newItems.create(on: req.db).transform(to: newItems).flatMap { savedItems in
                    return savedItems.sequencedFlatMapEach(on: req.eventLoop) { updatedTimeLineItem in
                        if let id = creator?.id {
                            updatedTimeLineItem.$creator.id = id
                        }
                        return updatedTimeLineItem.update(on: req.db).eventLoop.future(member)
                    }.flatMap { _ in
                        if isNewlyPregnant {
                            return createPregnatStatus(deliveryDate: input.deliveryDate ?? "").create(on: req.db).flatMap { _ in
                                return req.eventLoop.future(member)
                            }
                        } else {
                            return req.eventLoop.future(member)
                        }
                    }
                }
            }
        }
    }
    
    fileprivate func createPregnatStatus(deliveryDate: String) -> MemberStatus {
        return MemberStatus(name: "pregnant",
                            state: "pregnant",
                            kind: "pregnancy",
                            location: nil,
                            startDate: deliveryDate,
                            endDate: nil)
    }
    
    fileprivate func createReasonIfNeeded(req: Request, input: ReasonInput?, member: Member) throws -> EventLoopFuture<Member> {
        if let input {
            let reason = input.model()
            reason.$creator.id = UUID(uuidString: input.creatorID)
            return reason.create(on: req.db).transform(to: member)
        } else {
            return req.eventLoop.future(member)
        }
    }
    
    fileprivate func updatePhoneIfNeeded(req:Request, input:MembersUpdateInput, member:Member) throws -> EventLoopFuture<Member> {
        if let phone = input.phone {
            //NOTE: need to make sure the query has the realtion to phones.
            if let mainPhone = member.phones.filter({$0.label == "main"}).last {
                mainPhone.number = phone
                return mainPhone.update(on: req.db).transform(to: member)
            } else {
                let phoneNumber = PhoneNumber(label: "main", number: phone)
                return member.$phones.create(phoneNumber, on: req.db).transform(to: member)
            }
        } else {
            return req.eventLoop.future(member)
        }
    }
    
    fileprivate func updateTagsIfNeeded(req:Request, tags: [Tag], member: Member) throws -> EventLoopFuture<Member> {
        if tags.isEmpty {
            return req.eventLoop.future(member)
        } else {
            return member.tags.delete(on: req.db).transform(to: member).flatMap { member in
                member.$tags.create(tags, on: req.db).transform(to: member)
            }
        }
    }
    
    fileprivate func updateAddressIfNeeded(req:Request, input:MembersUpdateInput, member:Member) throws -> EventLoopFuture<Member> {
        return try AddressService.updateAddressIfNeeded(req: req, input: input.address, model: member).transform(to: member)
    }
    
    func updateTimeline(req: Request, memberStatus: MemberStatus, member: Member) throws -> EventLoopFuture<Void> {
        let kind = memberStatus.kind
        if kind == "pregnancy" {
            member.pregnancyStatus = nil
            member.deliveryDate = nil
            
            return member.update(on: req.db).flatMap { _ in
                
                let msg = "member has been unflagged as “pregnant”"
                let title = "Pregnancy Status Removed"
                let status = "pregnancy_removed"
                return TimelineItem(carepackageID: "",
                                    status: status,
                                    desc: msg,
                                    title: title,
                                    memberId: member.id,
                                    visible: true,
                                    meta: nil)
                .create(on: req.db)
            }
        } else if kind == "admission" {
            let msg = "member has been unflagged as “admitted to hospital”"
            let title = "Admission Status Removed"
            let status = "admission_removed"
            return TimelineItem(carepackageID: "",
                                status: status,
                                desc: msg,
                                title: title,
                                memberId: member.id,
                                visible: true,
                                meta: nil)
            .create(on: req.db)
            
            
        } else if kind == "homeless" {
            
            let msg = "member has been unflagged as “currently homeless”"
            let title = "Homeless Status Removed"
            let status = "homeless_removed"
            return TimelineItem(carepackageID: "",
                                status: status,
                                desc: msg,
                                title: title,
                                memberId: member.id,
                                visible: true,
                                meta: nil)
            .create(on: req.db)
        } else {
            return req.eventLoop.future()
        }
    }
    
    
    //MARK: - Delete
    func delete(req: Request, status: MemberStatus) throws -> EventLoopFuture<HTTPStatus> {
        return status.delete(on: req.db).transform(to: HTTPStatus.accepted)
    }
    
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .actions)
        return try AuthController.userIdFromToken(req: req).flatMap { navId in
            
            return try! find(req: req).flatMap { member in
                let memberId = member.id!
                return try! removeMemberFromAllHeadHousehold(req: req, memberId: memberId).flatMap { _ in
                    return detachAllMemberFromAllHouseholds(req: req, memberId: memberId).flatMap({ _ in
                        return member.delete(on: req.db).flatMap { _ in
                            return cloudwatch.putLog(message: logDeleteMessage(navId: navId,
                                                                               memberId: memberId.uuidString), on: req.eventLoop)
                            .transform(to: HTTPStatus.accepted)
                        }
                    })
                }
            }
        }
    }
    
    func removeMemberFromAllHeadHousehold(req: Request, memberId: UUID) throws ->  EventLoopFuture<Void> {
        return Household.query(on: req.db)
            .filter(\.$headOfHouse.$id == memberId)
            .all()
            .flatMap { households in
                households.map { household in
                    household.$headOfHouse.id = nil
                    return household.save(on: req.db)
                }.flatten(on: req.db.eventLoop)
            }
    }
    
    fileprivate func detachAllMemberFromAllHouseholds(req: Request, memberId: UUID) -> EventLoopFuture<Void> {
        return HouseholdMembers.query(on: req.db)
            .filter(\.$member.$id == memberId)
            .delete()
    }
    
    //MARK: - Query
    fileprivate func buildQuery( query: QueryBuilder<Member>, req:Request) throws -> QueryBuilder<Member> {
        var query = query
        let queryParams = try req.query.decode(MemberQuery.self)
        let sortBy = queryParams.sortBy ?? .createdAt
        let direction = queryParams.sortDirection ?? .asc
        
        //Filtering
        if let firstName = queryParams.firstName  {
            query.filter(\.$firstName, .custom("ilike"), "%\(firstName.lowercased())%")
        }
        
        if let lastName = queryParams.lastName  {
            query.filter(\.$lastName, .custom("ilike"), "%\(lastName.lowercased())%")
        }
        
        if let email = queryParams.email {
            query.filter(\.$email == email)
        }

        if let status = queryParams.status {
            query.filter(\.$status == status)
        }
        
        if let tags = queryParams.tags, !tags.isEmpty {
            query.join(Tag.self, on: \Member.$id == \Tag.$member.$id) // Join Member with Tag
            query.filter(Tag.self, \.$name ~~ tags)
            query.unique()
        }
        
        if let search = queryParams.search, !search.isEmpty {
            query.group(.or) { group in
                group.filter(\.$firstName, .custom("ilike"), "%\(search.lowercased())%")
                group.filter(\.$lastName, .custom("ilike"), "%\(search.lowercased())%")
            }
        }
        
        
        // Sorting
        switch (sortBy, direction) {
        case (.firstName, .asc): query = query.sort(\.$firstName, .ascending)
        case (.firstName, .desc): query = query.sort(\.$firstName, .descending)
        case (.dob, .asc): query = query.sort(\.$dob, .ascending)
        case (.dob, .desc): query = query.sort(\.$dob, .descending)
        case (.status, .asc): query = query.sort(\.$status, .ascending)
        case (.status, .desc): query = query.sort(\.$status, .descending)
        case (.score, .asc): query = query.sort(\.$score, .ascending)
        case (.score, .desc): query = query.sort(\.$score, .descending)
        case (.gender, .asc): query = query.sort(\.$gender, .ascending)
        case (.gender, .desc): query = query.sort(\.$gender, .descending)
        case (.lang, .asc): query = query.sort(\.$lang, .ascending)
        case (.lang, .desc): query = query.sort(\.$lang, .descending)
        case (.sexualIdentity, .asc): query = query.sort(\.$sexualIdentity, .ascending)
        case (.sexualIdentity, .desc): query = query.sort(\.$sexualIdentity, .descending)
        case (.genderIdentity, .asc): query = query.sort(\.$genderIdentity, .ascending)
        case (.genderIdentity, .desc): query = query.sort(\.$genderIdentity, .descending)
        case (.referredBy, .asc): query = query.sort(\.$referredBy, .ascending)
        case (.referredBy, .desc): query = query.sort(\.$referredBy, .descending)
        case (.military, .asc): query = query.sort(\.$military, .ascending)
        case (.military, .desc): query = query.sort(\.$military, .descending)
        case (.ethnicity, .asc): query = query.sort(\.$ethnicity, .ascending)
        case (.ethnicity, .desc): query = query.sort(\.$ethnicity, .descending)
        case (.email, .asc): query = query.sort(\.$email, .ascending)
        case (.email, .desc): query = query.sort(\.$email, .descending)
        case (.pronouns, .asc): query = query.sort(\.$pronouns, .ascending)
        case (.pronouns, .desc): query = query.sort(\.$pronouns, .descending)
        case (.lastContact, .asc): query = query.sort(\.$lastContact, .ascending)
        case (.lastContact, .desc): query = query.sort(\.$lastContact, .descending)
        case (.createdAt, .asc): query = query.sort(\.$createdAt, .ascending)
        case (.createdAt, .desc): query = query.sort(\.$createdAt, .descending)
        case (.updatedAt, .asc): query = query.sort(\.$updatedAt, .ascending)
        case (.updatedAt, .desc): query = query.sort(\.$updatedAt, .descending)
        }
        
        return query
    }
    
    static func findAuth(req: Request, auth:String) throws -> EventLoopFuture<Member> {
        return Member.query(on: req.db).filter(\.$auth == auth)
            .with(\.$org)
            .with(\.$households)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$attachments)
            .with(\.$primaryUser)
            .first().flatMapThrowing() {model in
                guard let foundModel = model else {  throw NetworkError.error(type: .user) }
                return foundModel
            }
    }
    
    fileprivate func consentTimelineitem(req: Request, _ member: Member, _ title: String, consentId: UUID?) throws -> EventLoopFuture<Member> {
        let item = TimeLineItemMessage.general(memberId: member.id!,
                                               title: "\(title.lowercased()) signed",
                                               desc: "consent \(title.lowercased()) has beend signed.",
                                               status: "consent_signed",
                                               visible: true,
                                               meta: .init(data: ["consent_id" : consentId?.uuidString ?? ""])).toTimelineItem()
        return try! AuthController.userFromToken(req: req).flatMap { user in
            return try! TimelineControllerController.create([item],
                                                            creatorId: user.id!,
                                                            req: req).transform(to: member)
        }
    }
}


func isProfile(type:String) -> Bool {
    return type.lowercased() == "profile" || type.lowercased() == "userProfile" || type.lowercased() == "pet"
}


//MARK: - CloudWatchLogs
extension MembersController {
    fileprivate func logDeleteMessage(navId: UUID, memberId: String) -> String {
        return CloudWatchLogMessage.send(msg: .actions(type: .delete,
                                                       model: "Member",
                                                       by: navId.uuidString,
                                                       source: "MemberId: \(memberId)"))
    }
}

struct TagCreateInput: Content {
    var memberId: String? = nil
    var tags: [TagInput]
}


struct TagsController:RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let tags = routes.grouped("tags")
        tags.post(use: create)
        tags.delete(":tagId", use: delete)
    }
    
    fileprivate func create(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let input = try req.content.decode(TagCreateInput.self)
        let allTags = input.tags.compactMap({$0.tag()})
        
        if let memberId = input.memberId {
            return try createMemberTags(allTags, req: req, memberId)
        } else {
            return allTags.create(on: req.db).transform(to: .accepted)
        }
    }
    
    fileprivate func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("tagId") else { throw NetworkError.error(type: .tag) }
        guard let tagID = UUID(id) else { throw NetworkError.error(type: .tag)}
        return Tag.query(on: req.db)
            .filter(\.$id == tagID)
            .with(\.$member)
            .first()
            .unwrap(or: Abort(.notFound))
            .flatMap { model in
                return model.delete(on: req.db).flatMap { _ in
                    //create timeline if tag is on members
                    if let memberId = model.member?.id {
                        return try! deleteMemberTag(model, req: req, memberId)
                    } else {
                        return req.eventLoop.future(.accepted)
                    }
                }
            }
    }
    
    fileprivate func timelineCreate(tags: String, memberId: UUID) -> TimelineItem {
        return TimeLineItemMessage.general(memberId: memberId,
                                           title: "new tag created",
                                           desc: "\(tags) tag was added",
                                           status: "added_tag",
                                           visible: true,
                                           meta: nil).toTimelineItem()
    }
    
    fileprivate func timelineDelete(tags: String, memberId: UUID) -> TimelineItem {
        return TimeLineItemMessage.general(memberId: memberId,
                                           title: "tag removed",
                                           desc: "\(tags) tag was removed",
                                           status: "removed_tag",
                                           visible: true,
                                           meta: nil).toTimelineItem()
    }
    
    fileprivate func deleteMemberTag(_ tag: Tag, req: Request, _ memberId: UUID) throws -> EventLoopFuture<HTTPStatus> {
        let titles = tag.name
        return try! AuthController.userFromToken(req: req).flatMap { user in
            try! TimelineControllerController.create([
                timelineDelete(tags: titles, memberId: memberId)
            ], creatorId: user.id!, req: req)
        }.transform(to: HTTPStatus.accepted)
    }
    
    fileprivate func createMemberTags(_ allTags:[Tag], req: Request, _ memberId: String) throws -> EventLoopFuture<HTTPStatus> {
        let uuid = UUID(uuidString: memberId)!
        let titles = allTags.compactMap({$0.name}).joined(separator: ",")
        
        return try MembersController.find(req: req, id: memberId).flatMap { member in
            
            return member.$tags.create(allTags, on: req.db).flatMap { _ in
                
                return try! AuthController.userFromToken(req: req).flatMap { user in
                    
                    return try! TimelineControllerController.create([
                        timelineCreate(tags: titles, memberId: uuid)
                    ], creatorId: user.id!, req: req).transform(to: HTTPStatus.accepted)
                }
            }
        }
    }
}


extension MembersController {
    
    func fetchMemberStatus(req: Request) throws -> EventLoopFuture<[MemberStatus]> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        let status:[String]? = req.query["status"]
        return try lookupAllMemberStatusForUser(id: id, status: status ?? [], req: req)
    }
    
    func createMemberStatus(req: Request) throws -> EventLoopFuture<MemberStatus> {
        guard let id = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        let input = try req.content.decode(MemberStatusInput.self)
        let status = MemberStatus(name: input.name,
                                  state: input.state,
                                  kind: input.kind,
                                  location: input.location,
                                  startDate: input.startDate,
                                  endDate: input.endDate)
        
        guard let state = input.state else { throw NetworkError.error(type: .badRequest, msg: "State is required") }
        
        return try findMember(id: id, req: req).flatMap { member in
            
            return try! lookupMemberStatusForUser(id:id,
                                                  status: state,
                                                  req: req).flatMap({ memberStatus in
                if memberStatus != nil {
                    let id = memberStatus?.id?.uuidString ?? ""
                    return req.eventLoop.makeFailedFuture(NetworkError.error(type: .badRequest,
                                                                             msg: "\(input.name ?? "" ) already exists. (\(id))") )
                } else {
                    return member.$memberStatus.create(status,
                                                       on: req.db).transform(to: status).flatMap { memberStatus in
                        
                        if let addressInput = input.address {
                            return memberStatus.$address.create(addressInput.address(), on: req.db).transform(to: memberStatus)
                        } else {
                            return req.eventLoop.future(memberStatus)
                        }
                    }
                }
            })
        }
    }
    
    func updateMemberStatus(req: Request) throws -> EventLoopFuture<MemberStatus> {
        let input = try req.content.decode(MemberStatusInput.self)
        guard let memberStatusId = input.id else { throw NetworkError.error(type: .status) }
        return try lookupMemberStatus(memberStatusId, req: req).flatMap { status in
            let data = input.returnUpdatedModel(status: status)
            return data.update(on: req.db).transform(to: data).flatMap { status in
                print("isStateChange: \(input.isStateChange(state: status.state ?? ""))")
                return try! self.updateMemberAddressIfNeeded(req: req, addressInput: input.address, status: status).transform(to: status)
            }
        }
    }
    
    func deleteStatus(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let memberId = req.parameters.get("memberID") else { throw NetworkError.error(type: .member) }
        guard let statusId = req.parameters.get("statusId") else { throw NetworkError.error(type: .status) }
        return try! findMember(id: memberId, req: req).flatMap { member in
            
            return try! findMemberStatus(id: statusId, req: req).flatMap { memberStatus in
                
                return try! createDeleteTimeline(req: req, memberStatus: memberStatus, member: member).flatMap { _ in
                    
                    return try! delete(req: req, status: memberStatus)
                    
                }
            }
        }
    }
    
    fileprivate func lookupMemberStatus(_ id: String, req: Request) throws -> EventLoopFuture<MemberStatus> {
        return MemberStatus.query(on: req.db)
            .filter(\.$id == UUID(id)!)
            .with(\.$address)
            .first().unwrap(or:  Abort(.notFound))
    }
    
    fileprivate func lookupMemberStatusForUser(id: String, status: String, req: Request) throws -> EventLoopFuture<MemberStatus?> {
        return MemberStatus.query(on: req.db)
            .filter(\.$member.$id == UUID(id)!)
            .filter(\.$state == status)
            .first()
    }
    
    fileprivate func lookupAllMemberStatusForUser(id: String, status: [String], req: Request) throws -> EventLoopFuture<[MemberStatus]> {
        return MemberStatus.query(on: req.db)
            .filter(\.$member.$id == UUID(id)!)
            .filter(\.$state ~~ status)
            .all()
    }
    
    func createDeleteTimeline(req: Request,
                              memberStatus: MemberStatus,
                              member: Member) throws -> EventLoopFuture<Void> {
        let kind = memberStatus.kind
        if kind == "pregnancy" {
            member.pregnancyStatus = nil
            member.deliveryDate = nil
            
            return member.update(on: req.db).flatMap { _ in
                
                let msg = "member has been unflagged as “pregnant”"
                let title = "Pregnancy Status Removed"
                let status = "pregnancy_removed"
                return TimelineItem(carepackageID: "",
                                    status: status,
                                    desc: msg,
                                    title: title,
                                    memberId: member.id,
                                    visible: true,
                                    meta: nil)
                .create(on: req.db)
            }
        } else if kind == "admission" {
            let msg = "member has been unflagged as “admitted to hospital”"
            let title = "Admission Status Removed"
            let status = "admission_removed"
            return TimelineItem(carepackageID: "",
                                status: status,
                                desc: msg,
                                title: title,
                                memberId: member.id,
                                visible: true,
                                meta: nil)
            .create(on: req.db)
            
            
        } else if kind == "homeless" {
            
            let msg = "member has been unflagged as “currently homeless”"
            let title = "Homeless Status Removed"
            let status = "homeless_removed"
            return TimelineItem(carepackageID: "",
                                status: status,
                                desc: msg,
                                title: title,
                                memberId: member.id,
                                visible: true,
                                meta: nil)
            .create(on: req.db)
        } else {
            return req.eventLoop.future()
        }
    }

    // MARK: - Primary User Management

    /// Set primary user for a member
    func setPrimaryUser(req: Request) throws -> EventLoopFuture<Member> {
        guard req.parameters.get("memberID") != nil else {
            throw NetworkError.error(type: .member)
        }

        let input = try req.content.decode(SetPrimaryUserInput.self)

        return try find(req: req).flatMap { member in
            // Validate that the user exists
            return User.query(on: req.db)
                .filter(\.$id == input.userID)
                .first()
                .flatMapThrowing { user in
                    guard user != nil else {
                        throw NetworkError.error(type: .user)
                    }
                    return user
                }
                .flatMap { _ in
                    // Set the primary user
                    member.$primaryUser.id = input.userID
                    return member.update(on: req.db).transform(to: member)
                }
        }
    }

    /// Remove primary user from a member
    func removePrimaryUser(req: Request) throws -> EventLoopFuture<Member> {
        guard req.parameters.get("memberID") != nil else {
            throw NetworkError.error(type: .member)
        }

        return try find(req: req).flatMap { member in
            member.$primaryUser.id = nil
            return member.update(on: req.db).transform(to: member)
        }
    }
}

// MARK: - Input Models

struct SetPrimaryUserInput: Content {
    let userID: UUID
}
