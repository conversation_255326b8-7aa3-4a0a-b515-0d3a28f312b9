import Vapor
import Fluent

struct AdminLoginRequest: Content {
    let username: String
    let password: String
}

struct AdminAuthController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let admin = routes.grouped("admin")
        
        // Login routes
        admin.get("login", use: showLogin)
        admin.post("login", use: login)
        admin.post("logout", use: logout)
        
        // Protected admin routes
        let protected = admin.grouped(AdminAuthMiddleware())
        protected.get("dashboard", use: dashboard)

        // Assessment templates endpoint for admin
        protected.get("templates", "assessment", use: getAssessmentTemplates)

        // Background check admin routes
        protected.get("background-checks", "queue", use: backgroundCheckQueue)

        // Organization Program Management routes
        protected.get("org-programs", use: orgProgramsDashboard)
        protected.get("org-programs", ":orgID", use: orgProgramsList)
        protected.get("org-programs", ":orgID", "create", use: showCreateProgramForm)
        protected.post("org-programs", ":orgID", "create", use: createProgram)
        protected.get("org-programs", ":orgID", ":programKey", use: viewProgram)
        protected.get("org-programs", ":orgID", ":programKey", "edit", use: showEditProgramForm)
        protected.put("org-programs", ":orgID", ":programKey", use: updateProgram)
        protected.delete("org-programs", ":orgID", ":programKey", use: deleteProgram)

        // Debug route to check session data
        protected.get("debug", "session", use: debugSession)
    }
    
    // MARK: - Show Login Form
    func showLogin(req: Request) throws -> EventLoopFuture<View> {
        // If already logged in, redirect to dashboard
        if req.session.data["admin_logged_in"] == "true" {
            return req.eventLoop.future(error: Abort.redirect(to: "/admin/dashboard"))
        }
        
        let context: [String: String] = [:]
        return req.view.render("admin-login", context)
    }
    
    // MARK: - Handle Login
    func login(req: Request) throws -> EventLoopFuture<Response> {
        let loginRequest = try req.content.decode(AdminLoginRequest.self)

        // Find user by username
        return AuthUser.query(on: req.db)
            .filter(\.$username == loginRequest.username)
            .first()
            .flatMap { authUser in
                guard let user = authUser else {
                    // User not found
                    let context = [
                        "error": "Invalid username or password",
                        "username": loginRequest.username
                    ]
                    return req.view.render("admin-login", context).flatMap { view in
                        return view.encodeResponse(status: .unauthorized, for: req)
                    }
                }

                // Verify password
                do {
                    let isValidPassword = try Bcrypt.verify(loginRequest.password, created: user.passwordHash)
                    guard isValidPassword else {
                        // Invalid password
                        let context = [
                            "error": "Invalid username or password",
                            "username": loginRequest.username
                        ]
                        return req.view.render("admin-login", context).flatMap { view in
                            return view.encodeResponse(status: .unauthorized, for: req)
                        }
                    }

                    // Create token for role verification
                    let token = try user.createToken(source: .login)
                    req.logger.info("Created token for user \(loginRequest.username): \(token.value.prefix(10))...")

                    // Save token to database first, then set session
                    return token.save(on: req.db).flatMap { _ in
                        req.logger.info("Token saved to database successfully")
                        // Set the token in the request for role verification
                        req.headers.bearerAuthorization = BearerAuthorization(token: token.value)

                        // Now verify admin role
                        return try! AdminAuthController.isVerifyAdminRole(req: req).flatMap { (userId, org, isAdmin) in
                            guard isAdmin else {
                                // Not an admin
                                let context = [
                                    "error": "Access denied. Admin privileges required.",
                                    "username": loginRequest.username
                                ]
                                return req.view.render("admin-login", context).flatMap { view in
                                    return view.encodeResponse(status: .forbidden, for: req)
                                }
                            }

                            // Login successful - set session data in proper order
                            req.session.data["org"] = org
                            req.session.data["admin_logged_in"] = "true"
                            req.session.data["admin_username"] = loginRequest.username
                            req.session.data["admin_login_time"] = String(Date().timeIntervalSince1970)

                            // Ensure token is set after all other session data
                            req.session.data["admin_token"] = token.value

                            req.logger.info("Admin login successful for user: \(loginRequest.username)")
                            req.logger.info("Admin token stored in session: \(token.value.prefix(10))...")
                            req.logger.info("Session data set: admin_logged_in=\(req.session.data["admin_logged_in"] ?? "nil"), token_length=\(token.value.count)")

                            // Verify session data is properly set before redirecting
                            guard req.session.data["admin_token"] == token.value else {
                                req.logger.error("Session token verification failed after setting!")
                                let context = [
                                    "error": "Session storage error",
                                    "username": loginRequest.username
                                ]
                                return req.view.render("admin-login", context).flatMap { view in
                                    return view.encodeResponse(status: .internalServerError, for: req)
                                }
                            }

                            // Redirect to dashboard
                            return req.eventLoop.future(req.redirect(to: "/admin/dashboard"))
                        }
                    }
                } catch {
                    req.logger.error("Password verification error: \(error)")
                    let context = [
                        "error": "Authentication error",
                        "username": loginRequest.username
                    ]
                    return req.view.render("admin-login", context).flatMap { view in
                        return view.encodeResponse(status: .internalServerError, for: req)
                    }
                }
            }
    }
    
    // MARK: - Get Assessment Templates
    func getAssessmentTemplates(req: Request) throws -> EventLoopFuture<[Template]> {
        guard let orgId = req.query[String.self, at: "org"] else {
            throw Abort(.badRequest, reason: "Organization ID is required")
        }

        return Template.query(on: req.db)
            .filter(\.$orgID == orgId.lowercased())
            .filter(\.$kind == "assessment")
            .all()
    }

    // MARK: - Handle Logout
    func logout(req: Request) throws -> EventLoopFuture<Response> {
        // Optionally invalidate the token in the database
        if let token = req.session.data["admin_token"] {
            return Token.query(on: req.db)
                .filter(\.$value == token)
                .delete()
                .flatMap { _ in
                    req.session.destroy()
                    return req.eventLoop.future(req.redirect(to: "/admin/login"))
                }
        } else {
            req.session.destroy()
            return req.eventLoop.future(req.redirect(to: "/admin/login"))
        }
    }
    
    // MARK: - Admin Dashboard
    func dashboard(req: Request) throws -> EventLoopFuture<Response> {
        // Get the authenticated user first, then query their organization
        return try AuthController.userFromToken(req: req).flatMap { user in
            guard let userOrgId = user.$org.id else {
                let context = AdminDashboardContext(
                    adminUsername: req.session.data["admin_username"] ?? "Admin",
                    organizations: []
                )
                return req.view.render("admin-dashboard", context).flatMap { view in
                    return view.encodeResponse(for: req)
                }
            }

            // For now, show only the user's organization
            // TODO: Later we can expand this to show all organizations for super admins
            return Organization.find(userOrgId, on: req.db)
                .flatMap { organization in
                    let organizations = organization != nil ? [organization!] : []
                    let context = AdminDashboardContext(
                        adminUsername: req.session.data["admin_username"] ?? "Admin",
                        organizations: organizations.map { org in
                            OrganizationInfo(
                                id: org.id?.uuidString ?? "",
                                name: org.title
                            )
                        }
                    )

                    return req.view.render("admin-dashboard", context)
                }
                .flatMap { view in
                    return view.encodeResponse(for: req)
                }
        }
    }

    // MARK: - Background Check Queue
    func backgroundCheckQueue(req: Request) throws -> EventLoopFuture<Response> {
        return BackgroundCheckReviewQueue.query(on: req.db)
            .filter(\.$reviewStatus == .pending)
            .with(\.$member)
            .with(\.$triggeredBy)
            .sort(\.$createdAt, .descending)
            .all()
            .flatMap { queueItems in
                let pendingCount = queueItems.count
                let totalMatches = queueItems.reduce(0) { $0 + $1.matchedCount }
                let highRiskCount = queueItems.filter { $0.highestRiskLevel?.contains("III") == true }.count

                let formattedItems = queueItems.map { item in
                    BackgroundCheckQueueItemView(
                        id: item.id!.uuidString,
                        member: item.member.map { member in
                            MemberView(
                                firstName: member.firstName,
                                lastName: member.lastName,
                                email: member.email
                            )
                        },
                        household: item.household.map { household in
                            HouseholdView(
                                id: household.id!.uuidString,
                                title: household.title,
                                memberCount: household.members.count
                            )
                        },
                        targetType: item.targetType.rawValue,
                        triggeredBy: UserView(
                            firstName: item.triggeredBy.firstName,
                            lastName: item.triggeredBy.lastName
                        ),
                        fullName: item.fullName,
                        zipCode: item.zipCode,
                        matchedCount: item.matchedCount,
                        highestRiskLevel: item.highestRiskLevel,
                        riskLevelClass: self.getRiskLevelClass(item.highestRiskLevel),
                        createdAtFormatted: self.formatDate(item.createdAt!)
                    )
                }

                let adminToken = req.session.data["admin_token"] ?? ""
                req.logger.info("Background check queue - Admin token from session: \(adminToken.isEmpty ? "EMPTY" : "Present (\(adminToken.prefix(10))...)")")

                let context = BackgroundCheckQueueContext(
                    adminUsername: req.session.data["admin_username"] ?? "Admin",
                    adminToken: adminToken,
                    pendingCount: pendingCount,
                    totalMatches: totalMatches,
                    highRiskCount: highRiskCount,
                    queueItems: formattedItems
                )

                return req.view.render("background-check-queue", context)
                    .flatMap { view in
                        return view.encodeResponse(for: req)
                    }
            }
    }

    // MARK: - Debug Session
    func debugSession(req: Request) throws -> EventLoopFuture<Response> {
        let sessionData = req.session.data
        let adminToken = sessionData["admin_token"] ?? ""
        let tokenDisplay = adminToken.isEmpty ? "EMPTY or nil" : "Present (\(adminToken.prefix(10))...)"

        var debugInfo: [String: String] = [:]
        debugInfo["admin_logged_in"] = sessionData["admin_logged_in"] ?? "nil"
        debugInfo["admin_username"] = sessionData["admin_username"] ?? "nil"
        debugInfo["admin_token"] = tokenDisplay
        debugInfo["admin_login_time"] = sessionData["admin_login_time"] ?? "nil"
        debugInfo["org"] = sessionData["org"] ?? "nil"
        debugInfo["session_id"] = req.session.id?.string ?? "nil"

        return req.eventLoop.future().flatMapThrowing {
            let jsonData = try JSONSerialization.data(withJSONObject: debugInfo, options: .prettyPrinted)
            let jsonString = String(data: jsonData, encoding: .utf8) ?? "Error encoding JSON"

            let response = Response(status: .ok, headers: ["Content-Type": "application/json"])
            response.body = .init(string: jsonString)
            return response
        }
    }

    private func getRiskLevelClass(_ riskLevel: String?) -> String {
        guard let riskLevel = riskLevel else { return "" }
        if riskLevel.contains("III") { return "3" }
        if riskLevel.contains("II") { return "2" }
        if riskLevel.contains("I") { return "1" }
        return ""
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    // MARK: - Helper Methods
    private func verifyOrgAccess(req: Request, orgID: UUID) throws -> EventLoopFuture<Void> {
        return try AuthController.userFromToken(req: req).flatMapThrowing { user in
            // Check if user belongs to this organization
            guard user.$org.id == orgID else {
                throw Abort(.forbidden, reason: "Access denied to this organization")
            }
            return ()
        }
    }

    private func verifyAdminAccess(req: Request, orgID: UUID) throws -> EventLoopFuture<Void> {
        return try AuthController.userFromToken(req: req).flatMapThrowing { user in
            req.logger.info("verifyAdminAccess - User: \(user.email), UserOrgID: \(user.$org.id?.uuidString ?? "nil"), RequestedOrgID: \(orgID.uuidString)")

            // Check if user belongs to this organization
            guard user.$org.id == orgID else {
                req.logger.error("verifyAdminAccess - Access denied: user org \(user.$org.id?.uuidString ?? "nil") != requested org \(orgID.uuidString)")
                throw Abort(.forbidden, reason: "Access denied to this organization")
            }

            // Check if user has admin role
            guard let roles = user.roles,
                  roles.contains("drop_all") ||
                  roles.contains(where: { $0.localizedCaseInsensitiveContains("admin") })
            else {
                req.logger.error("verifyAdminAccess - Admin access required: user roles = \(user.roles ?? [])")
                throw Abort(.forbidden, reason: "Admin access required")
            }

            req.logger.info("verifyAdminAccess - Success for user \(user.email)")
            return ()
        }
    }
    
    static func isVerifyAdminRole(req: Request) throws -> EventLoopFuture<(String?, String?, Bool)> {
        return try AuthController.userFromToken(req: req).flatMapThrowing { user in
            let userOrg = user.$org.id?.uuidString
            req.logger.info("verifyAdminAccess - User: \(user.email), UserOrgID: \(userOrg ?? "nil")")
            let userId = user.id?.uuidString

            // Check if user has admin role
            guard let roles = user.roles,
                  roles.contains("drop_all") ||
                  roles.contains(where: { $0.localizedCaseInsensitiveContains("admin") })
            else {
                req.logger.error("verifyAdminAccess - Admin access required: user roles = \(user.roles ?? [])")
                return (userId, userOrg, false)
            }

            req.logger.info("verifyAdminAccess - Success for user \(user.email)")
            return (userId, userOrg, true)
        }
    }

    // MARK: - Organization Program Management

    // Organization selection dashboard
    func orgProgramsDashboard(req: Request) throws -> EventLoopFuture<Response> {
        return try AuthController.userFromToken(req: req).flatMap { user in
            guard let userOrgId = user.$org.id else {
                let context = OrgProgramsDashboardContext(
                    adminUsername: req.session.data["admin_username"] ?? "Admin",
                    organizations: []
                )
                return req.view.render("org-programs-dashboard", context).flatMap { view in
                    return view.encodeResponse(for: req)
                }
            }

            // For now, show only the user's organization
            return Organization.find(userOrgId, on: req.db)
                .flatMap { organization in
                    let organizations = organization != nil ? [organization!] : []
                    let context = OrgProgramsDashboardContext(
                        adminUsername: req.session.data["admin_username"] ?? "Admin",
                        organizations: organizations.map { org in
                            OrganizationInfo(
                                id: org.id?.uuidString ?? "",
                                name: org.title
                            )
                        }
                    )

                    return req.view.render("org-programs-dashboard", context)
                }
                .flatMap { view in
                    return view.encodeResponse(for: req)
                }
        }
    }

    // List programs for organization
    func orgProgramsList(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgID = req.parameters.get("orgID") else {
            throw Abort(.badRequest, reason: "Invalid organization ID")
        }

        // Debug logging
        req.logger.info("🔍 DEBUG: Looking for programs with orgID: \(orgID)")

        return try AuthController.userFromToken(req: req).flatMap { user in
            // Get organization details
            return Organization.query(on: req.db)
                .filter(\.$id == UUID(uuidString: orgID)!)
                .first()
                .flatMapThrowing { organization in
                    guard let org = organization else {
                        throw Abort(.notFound, reason: "Organization not found")
                    }
                    req.logger.info("🔍 DEBUG: Found organization: \(org.title) with UUID: \(org.id?.uuidString ?? "nil")")
                    return org
                }
                .flatMap { org in
                    // First, let's check what orgId values exist in the database
                    return OrgProgram.query(on: req.db)
                                .filter(\.$orgId == orgID.lowercased())
                                .sort(\.$createdAt, .descending)
                                .all()
                                .flatMap { programs in
                                    req.logger.info("🔍 DEBUG: Found \(programs.count) programs for orgID: \(orgID)")

                                    let context = OrgProgramsListContext(
                                        adminUsername: req.session.data["admin_username"] ?? "Admin",
                                        orgID: orgID,
                                        orgName: org.title,
                                        programs: programs.map { program in
                                            OrgProgramListItem(
                                                programName: program.programName,
                                                programKey: program.programKey,
                                                programType: program.programConfig.programType,
                                                description: program.programConfig.description,
                                                createdAt: program.createdAt?.formatted() ?? ""
                                            )
                                        }
                                    )

                                    return req.view.render("org-programs-list", context)
                                }
                                .flatMap { (view: View) in
                                    return view.encodeResponse(for: req)
                                }
                }
        }
    }

    // Show create program form
    func showCreateProgramForm(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgID = req.parameters.get("orgID") else {
            throw Abort(.badRequest, reason: "Invalid organization ID")
        }

        return try AuthController.userFromToken(req: req).flatMap { user in
            return Organization.query(on: req.db)
                .filter(\.$id == UUID(uuidString: orgID)!)
                .first()
                .flatMapThrowing { organization in
                    guard let org = organization else {
                        throw Abort(.notFound, reason: "Organization not found")
                    }
                    return org
                }
                .flatMap { org in

                    let context = OrgProgramFormContext(
                        adminUsername: req.session.data["admin_username"] ?? "Admin",
                        orgID: orgID,
                        orgName: org.title,
                        isEdit: false,
                        program: nil
                    )

                    return req.view.render("org-program-form", context)
                }
                .flatMap { (view: View) in
                    return view.encodeResponse(for: req)
                }
        }
    }

    // Handle create program
    func createProgram(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgID = req.parameters.get("orgID") else {
            throw Abort(.badRequest, reason: "Invalid organization ID")
        }

        struct CreateProgramRequest: Content {
            let programName: String
            let programKey: String
            let programConfig: String
        }

        do {
            let createRequest = try req.content.decode(CreateProgramRequest.self)

            // Parse JSON configuration
            guard let configData = createRequest.programConfig.data(using: .utf8),
                  let programConfig = try? JSONDecoder().decode(ProgramConfig.self, from: configData) else {
                throw Abort(.badRequest, reason: "Invalid JSON configuration")
            }

            // Create the program directly using the OrgProgram model
            let orgProgram = OrgProgram(
                orgId: orgID,
                programName: createRequest.programName,
                programKey: createRequest.programKey,
                programConfig: programConfig
            )

            return orgProgram.save(on: req.db).map { _ in
                return req.redirect(to: "/admin/org-programs/\(orgID)")
            }
        } catch {
            throw Abort(.badRequest, reason: "Invalid request data")
        }
    }

    // View program details
    func viewProgram(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgID = req.parameters.get("orgID"),
              let programKey = req.parameters.get("programKey") else {
            throw Abort(.badRequest, reason: "Invalid organization ID or program key")
        }

        // Get program directly from database
        return OrgProgram.query(on: req.db)
            .filter(\.$orgId == orgID.lowercased())
            .filter(\.$programKey == programKey)
            .first()
            .flatMapThrowing { program in
                guard let program = program else {
                    throw Abort(.notFound, reason: "Program not found")
                }
                return program
            }
            .flatMap { program in
                return Organization.query(on: req.db)
                    .filter(\.$id == UUID(uuidString: orgID)!)
                    .first()
                    .flatMapThrowing { organization in
                        guard let org = organization else {
                            throw Abort(.notFound, reason: "Organization not found")
                        }
                        return org
                    }
                    .flatMap { org in
                        let configData = try! JSONEncoder().encode(program.programConfig)
                        let context = OrgProgramDetailContext(
                            adminUsername: req.session.data["admin_username"] ?? "Admin",
                            orgID: orgID,
                            orgName: org.title,
                            program: OrgProgramDetailItem(
                                programName: program.programName,
                                programKey: program.programKey,
                                programConfig: configData.prettyPrintedJSONString()
                            )
                        )

                        return req.view.render("org-program-detail", context)
                            .flatMap { (view: View) in
                                return view.encodeResponse(for: req)
                            }
                    }
            }
    }

    // Show edit program form
    func showEditProgramForm(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgID = req.parameters.get("orgID"),
              let programKey = req.parameters.get("programKey") else {
            throw Abort(.badRequest, reason: "Invalid organization ID or program key")
        }

        // Get program directly from database
        return OrgProgram.query(on: req.db)
            .filter(\.$orgId == orgID.lowercased())
            .filter(\.$programKey == programKey)
            .first()
            .flatMapThrowing { program in
                guard let program = program else {
                    throw Abort(.notFound, reason: "Program not found")
                }
                return program
            }
            .flatMap { program in
                return Organization.query(on: req.db)
                    .filter(\.$id == UUID(uuidString: orgID)!)
                    .first()
                    .flatMapThrowing { organization in
                        guard let org = organization else {
                            throw Abort(.notFound, reason: "Organization not found")
                        }
                        return org
                    }
                    .flatMap { org in
                        let configData = try! JSONEncoder().encode(program.programConfig)
                        let context = OrgProgramFormContext(
                            adminUsername: req.session.data["admin_username"] ?? "Admin",
                            orgID: orgID,
                            orgName: org.title,
                            isEdit: true,
                            program: OrgProgramDetailItem(
                                programName: program.programName,
                                programKey: program.programKey,
                                programConfig: configData.prettyPrintedJSONString()
                            )
                        )

                        return req.view.render("org-program-form", context)
                            .flatMap { (view: View) in
                                return view.encodeResponse(for: req)
                            }
                    }
            }
    }

    // Handle update program
    func updateProgram(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgID = req.parameters.get("orgID"),
              let programKey = req.parameters.get("programKey") else {
            throw Abort(.badRequest, reason: "Invalid organization ID or program key")
        }

        struct UpdateProgramRequest: Content {
            let programName: String
            let programConfig: String
        }

        do {
            let updateRequest = try req.content.decode(UpdateProgramRequest.self)

            // Parse JSON configuration
            guard let configData = updateRequest.programConfig.data(using: .utf8),
                  let programConfig = try? JSONDecoder().decode(ProgramConfig.self, from: configData) else {
                throw Abort(.badRequest, reason: "Invalid JSON configuration")
            }

            // Update program directly in database
            return OrgProgram.query(on: req.db)
                .filter(\.$orgId == orgID.lowercased())
                .filter(\.$programKey == programKey)
                .first()
                .flatMapThrowing { program in
                    guard let program = program else {
                        throw Abort(.notFound, reason: "Program not found")
                    }
                    return program
                }
                .flatMap { program in
                    program.programName = updateRequest.programName
                    program.programConfig = programConfig

                    return program.save(on: req.db).map { _ in
                        return req.redirect(to: "/admin/org-programs/\(orgID)/\(programKey)")
                    }
                }
        } catch {
            throw Abort(.badRequest, reason: "Invalid request data")
        }
    }

    // Handle delete program
    func deleteProgram(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgID = req.parameters.get("orgID"),
              let programKey = req.parameters.get("programKey") else {
            throw Abort(.badRequest, reason: "Invalid organization ID or program key")
        }

        // Delete program directly from database
        return OrgProgram.query(on: req.db)
            .filter(\.$orgId == orgID.lowercased())
            .filter(\.$programKey == programKey)
            .first()
            .flatMapThrowing { program in
                guard let program = program else {
                    throw Abort(.notFound, reason: "Program not found")
                }
                return program
            }
            .flatMap { program in
                return program.delete(on: req.db).map { _ in
                    return req.redirect(to: "/admin/org-programs/\(orgID)")
                }
            }
    }
}

// MARK: - Admin Auth Middleware
struct AdminAuthMiddleware: Middleware {
    func respond(to request: Request, chainingTo next: Responder) -> EventLoopFuture<Response> {
        // Check if admin is logged in
        guard request.session.data["admin_logged_in"] == "true",
              let token = request.session.data["admin_token"] else {
            return request.eventLoop.future(request.redirect(to: "/admin/login"))
        }

        // Check session timeout (24 hours)
        if let loginTimeString = request.session.data["admin_login_time"],
           let loginTime = Double(loginTimeString) {
            let sessionTimeout: TimeInterval = 24 * 60 * 60 // 24 hours
            if Date().timeIntervalSince1970 - loginTime > sessionTimeout {
                request.session.destroy()
                return request.eventLoop.future(request.redirect(to: "/admin/login"))
            }
        }

        // Set the bearer token for downstream authentication
        request.headers.bearerAuthorization = BearerAuthorization(token: token)

        return next.respond(to: request)
    }
}

// MARK: - Context Structs
struct AdminDashboardContext: Codable {
    let adminUsername: String
    let organizations: [OrganizationInfo]
}

struct OrganizationInfo: Codable {
    let id: String
    let name: String
}

struct OrgProgramsDashboardContext: Codable {
    let adminUsername: String
    let organizations: [OrganizationInfo]
}

struct OrgProgramsListContext: Codable {
    let adminUsername: String
    let orgID: String
    let orgName: String
    let programs: [OrgProgramListItem]
}

struct OrgProgramListItem: Codable {
    let programName: String
    let programKey: String
    let programType: String
    let description: String
    let createdAt: String
}

struct OrgProgramFormContext: Codable {
    let adminUsername: String
    let orgID: String
    let orgName: String
    let isEdit: Bool
    let program: OrgProgramDetailItem?
}

struct OrgProgramDetailItem: Codable {
    let programName: String
    let programKey: String
    let programConfig: String
}

struct OrgProgramDetailContext: Codable {
    let adminUsername: String
    let orgID: String
    let orgName: String
    let program: OrgProgramDetailItem
}

// Helper extension for JSON pretty printing
extension Data {
    func prettyPrintedJSONString() -> String {
        guard let object = try? JSONSerialization.jsonObject(with: self, options: []),
              let data = try? JSONSerialization.data(withJSONObject: object, options: [.prettyPrinted]),
              let prettyPrintedString = String(data: data, encoding: .utf8) else {
            return String(data: self, encoding: .utf8) ?? ""
        }
        return prettyPrintedString
    }
}

// MARK: - Background Check Queue Context
struct BackgroundCheckQueueContext: Codable {
    let adminUsername: String
    let adminToken: String
    let pendingCount: Int
    let totalMatches: Int
    let highRiskCount: Int
    let queueItems: [BackgroundCheckQueueItemView]
}

struct BackgroundCheckQueueItemView: Codable {
    let id: String
    let member: MemberView?
    let household: HouseholdView?
    let targetType: String
    let triggeredBy: UserView
    let fullName: String
    let zipCode: String
    let matchedCount: Int
    let highestRiskLevel: String?
    let riskLevelClass: String
    let createdAtFormatted: String
}

struct MemberView: Codable {
    let firstName: String
    let lastName: String
    let email: String
}

struct HouseholdView: Codable {
    let id: String
    let title: String
    let memberCount: Int
}

struct UserView: Codable {
    let firstName: String
    let lastName: String
}
