<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organization Program Management - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #E5E7EB;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-info {
            color: #6B7280;
            font-size: 14px;
        }
        
        .logout-btn {
            background: #EF4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .logout-btn:hover {
            background: #DC2626;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            color: #6B7280;
            font-size: 16px;
            margin-bottom: 40px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: #3B82F6;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .breadcrumb-separator {
            color: #9CA3AF;
        }
        
        .org-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }
        
        .org-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #E5E7EB;
            transition: all 0.2s;
        }
        
        .org-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        
        .org-name {
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .org-id {
            color: #6B7280;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .manage-btn {
            display: inline-block;
            background: #1470C4;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .manage-btn:hover {
            background: #0F5A9C;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 12px;
            border: 1px solid #E5E7EB;
        }
        
        .empty-state h3 {
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .empty-state p {
            color: #6B7280;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-text">Wellup</div>
            </div>
            
            <div class="user-menu">
                <span class="user-info">Welcome, #(adminUsername)</span>
                <form method="POST" action="/admin/logout" style="display: inline;">
                    <button type="submit" class="logout-btn">Logout</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="breadcrumb">
            <a href="/admin/dashboard">Admin Dashboard</a>
            <span class="breadcrumb-separator">›</span>
            <span>Organization Program Management</span>
        </div>
        
        <h1 class="page-title">Organization Program Management</h1>
        <p class="page-subtitle">Select an organization to manage its programs, assessments, and configurations</p>

        #if(count(organizations) > 0):
        <div class="org-grid">
            #for(org in organizations):
            <div class="org-card">
                <h3 class="org-name">#(org.name)</h3>
                <div class="org-id">Manage organization-level programs with custom assessments, reviews, and JSONB configurations.</div>
                <a href="/admin/org-programs/#(org.id)" class="manage-btn">
                    Manage Programs
                </a>
            </div>
            #endfor
        </div>
        #else:
        <div class="empty-state">
            <h3>No Organizations Found</h3>
            <p>There are no organizations available to manage programs for.</p>
        </div>
        #endif
    </div>
</body>
</html>
