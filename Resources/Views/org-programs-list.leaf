<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Programs - #(orgName) - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #E5E7EB;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-info {
            color: #6B7280;
            font-size: 14px;
        }
        
        .logout-btn {
            background: #EF4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .logout-btn:hover {
            background: #DC2626;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: #3B82F6;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .breadcrumb-separator {
            color: #9CA3AF;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 32px;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            color: #6B7280;
            font-size: 16px;
        }
        
        .create-btn {
            background: #1470C4;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            transition: background-color 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .create-btn:hover {
            background: #0F5A9C;
        }
        
        .programs-grid {
            display: grid;
            gap: 20px;
        }
        
        .program-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #E5E7EB;
            transition: all 0.2s;
        }
        
        .program-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .program-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }
        
        .program-title {
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 4px;
        }
        
        .program-key {
            font-size: 14px;
            color: #6B7280;
            font-family: 'Monaco', 'Menlo', monospace;
            background: #F3F4F6;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .program-type {
            background: #E8F4FD;
            color: #1470C4;
            border: 1px solid #B8E0FF;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .program-description {
            color: #6B7280;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
        }
        
        .program-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #9CA3AF;
            margin-bottom: 16px;
        }
        
        .program-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: none;
            border: 1px solid #1470C4;
            color: #1470C4;
        }

        .btn-primary:hover {
            background: #1470C4;
            color: white;
        }

        .btn-secondary {
            background: none;
            border: 1px solid #6B7280;
            color: #6B7280;
        }

        .btn-secondary:hover {
            background: #6B7280;
            color: white;
        }

        .btn-danger {
            background: none;
            border: 1px solid #E97100;
            color: #E97100;
        }

        .btn-danger:hover {
            background: #E97100;
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 12px;
            border: 1px solid #E5E7EB;
        }
        
        .empty-state h3 {
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .empty-state p {
            color: #6B7280;
            font-size: 16px;
            margin-bottom: 24px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-text">Wellup</div>
            </div>
            
            <div class="user-menu">
                <span class="user-info">Welcome, #(adminUsername)</span>
                <form method="POST" action="/admin/logout" style="display: inline;">
                    <button type="submit" class="logout-btn">Logout</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="breadcrumb">
            <a href="/admin/dashboard">Admin Dashboard</a>
            <span class="breadcrumb-separator">›</span>
            <a href="/admin/org-programs">Organization Program Management</a>
            <span class="breadcrumb-separator">›</span>
            <span>#(orgName)</span>
        </div>
        
        <div class="page-header">
            <div>
                <h1 class="page-title">Programs for #(orgName)</h1>
                <p class="page-subtitle">Manage organization-level programs with custom assessments and configurations</p>
            </div>
            <a href="/admin/org-programs/#(orgID)/create" class="create-btn">
                + Create New Program
            </a>
        </div>

        #if(count(programs) > 0):
        <div class="programs-grid">
            #for(program in programs):
            <div class="program-card">
                <div class="program-header">
                    <div>
                        <div class="program-title">#(program.programName)</div>                        
                    </div>
                    <div class="program-type">#(program.programType)</div>
                </div>
                
                <div class="program-description">#(program.description)</div>
                
                <div class="program-meta">
                    <span>Created: #(program.createdAt)</span>
                </div>
                
                <div class="program-actions">
                    <a href="/admin/org-programs/#(orgID)/#(program.programKey)" class="btn btn-primary">View Details</a>
                    <a href="/admin/org-programs/#(orgID)/#(program.programKey)/edit" class="btn btn-secondary">Edit</a>
                    <form method="POST" action="/admin/org-programs/#(orgID)/#(program.programKey)" style="display: inline;">
                        <input type="hidden" name="_method" value="DELETE">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this program? This action cannot be undone.')">Delete</button>
                    </form>
                </div>
            </div>
            #endfor
        </div>
        #else:
        <div class="empty-state">
            <h3>No Programs Found</h3>
            <p>This organization doesn't have any programs yet. Create your first program to get started.</p>
            <a href="/admin/org-programs/#(orgID)/create" class="create-btn">
                + Create First Program
            </a>
        </div>
        #endif
    </div>
</body>
</html>
