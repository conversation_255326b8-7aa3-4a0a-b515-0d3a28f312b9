<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assessment Creator - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #E5E7EB;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            margin-right: 12px;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }
        
        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6B7280;
            font-size: 14px;
        }
        
        .nav-breadcrumb a {
            color: #FD8205;
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .user-info {
            color: #6B7280;
            font-size: 14px;
        }
        
        .logout-btn {
            background: #EF4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .logout-btn:hover {
            background: #DC2626;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            font-size: 16px;
            color: #6B7280;
        }
        
        .create-btn {
            background: #FD8205;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .create-btn:hover {
            background: #2563EB;
            transform: translateY(-1px);
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
        }
        
        .template-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 24px;
            transition: all 0.2s;
            border: 1px solid #E5E7EB;
        }
        
        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }
        
        .template-name {
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 4px;
        }
        
        .template-key {
            font-size: 12px;
            color: #6B7280;
            background: #F3F4F6;
            padding: 2px 8px;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .template-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-active {
            background: #D1FAE5;
            color: #065F46;
        }
        
        .status-draft {
            background: #FEF3C7;
            color: #92400E;
        }
        
        .status-archived {
            background: #F3F4F6;
            color: #6B7280;
        }
        
        .template-meta {
            display: flex;
            flex-direction: column;
            gap: 0.25em;            
            margin-bottom: 16px;
            font-size: 14px;
            color: #6B7280;
        }
        
        .template-meta span {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .template-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .btn-primary {
            background: #FD8205;
            color: white;
        }
        
        .btn-primary:hover {
            background: #E97100;
        }
        
        .btn-secondary {
            background: #F3F4F6;
            color: #374151;
        }
        
        .btn-secondary:hover {
            background: #E5E7EB;
        }
        
        .btn-danger {
            background: #E42B57;
            color: white;
        }
        
        .btn-danger:hover {
            background: #E42B57;
        }
        
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            background: white;
            border-radius: 12px;
            border: 2px dashed #E5E7EB;
        }
        
        .empty-state h3 {
            font-size: 24px;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .empty-state p {
            color: #6B7280;
            margin-bottom: 24px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
        }
        
        .close {
            color: #6B7280;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">                        
            <div class="nav-breadcrumb">
                <a href="/admin/dashboard">Admin Dashboard</a>
                <span>›</span>
                <span>Assessment Creator</span>
            </div>
            
            <div class="user-menu">
                <span class="user-info">Welcome, #(adminUsername)</span>
                <form method="POST" action="/admin/logout" style="display: inline;">
                    <button type="submit" class="logout-btn">Logout</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="page-header">
            <div>
                <h1 class="page-title">Assessment Creator</h1>
                <p class="page-subtitle">Build and manage custom assessment templates for your organization</p>
            </div>
            <a href="/admin/assessment-creator/create" class="create-btn">
                ➕ Create New Assessment
            </a>
        </div>
        
        #if(count(templates) > 0):
        <div class="templates-grid">
            #for(template in templates):
            <div class="template-card">
                <div class="template-header">
                    <div>
                        <h3 class="template-name">#(template.name)</h3>                        
                    </div>
                    <span class="template-status status-#(template.status)">#(template.status)</span>
                </div>
                
                <div class="template-meta">
                    <span>Language:</b> <b style="color: #1F2937; font-weight: 500;">#(template.language)</b></span>
                    <span>Scored: <b style="color: #1F2937; font-weight: 500;">#if(template.scored): Scored#else: Unscored#endif</b></span>
                    <span>Last Updated: <b style="color: #1F2937; font-weight: 500;">#(template.updatedAt)</b></span>
                </div>
                
                <div class="template-actions">
                    <a href="/admin/assessment-creator/#(template.id)/edit" class="btn btn-primary">Edit</a>
                    <a href="/admin/assessment-creator/#(template.id)/edit" class="btn btn-secondary">View</a>
                    <button onclick="duplicateTemplate('#(template.id)')" class="btn btn-secondary">Duplicate</button>
                    <button onclick="confirmDelete('#(template.id)', '#(template.name)')" class="btn btn-danger">Delete</button>
                </div>
            </div>
            #endfor
        </div>
        #else:
        <div class="empty-state">
            <h3>No Assessment Templates</h3>
            <p>Get started by creating your first custom assessment template</p>
            <a href="/admin/assessment-creator/create" class="create-btn">
                ➕ Create Your First Assessment
            </a>
        </div>
        #endif
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <span class="close" onclick="closeModal('deleteModal')">&times;</span>
            </div>
            <p>Are you sure you want to delete "<span id="deleteTemplateName"></span>"?</p>
            <p style="color: #EF4444; font-size: 14px; margin-top: 8px;">This action cannot be undone.</p>
            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                <button onclick="closeModal('deleteModal')" class="btn btn-secondary">Cancel</button>
                <button id="confirmDeleteBtn" class="btn btn-danger">Delete</button>
            </div>
        </div>
    </div>

    <script>
        let currentDeleteId = '';
        
        function confirmDelete(templateId, templateName) {
            currentDeleteId = templateId;
            document.getElementById('deleteTemplateName').textContent = templateName;
            document.getElementById('deleteModal').style.display = 'block';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        document.getElementById('confirmDeleteBtn').addEventListener('click', async function() {
            if (currentDeleteId) {
                try {
                    const response = await fetch(`/admin/assessment-creator/${currentDeleteId}`, {
                        method: 'DELETE'
                    });
                    
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Failed to delete template. Please try again.');
                    }
                } catch (error) {
                    console.error('Delete error:', error);
                    alert('An error occurred. Please try again.');
                }
            }
        });
        
        async function duplicateTemplate(templateId) {
            try {
                const response = await fetch(`/admin/assessment-creator/${templateId}/duplicate`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    window.location.reload();
                } else {
                    alert('Failed to duplicate template. Please try again.');
                }
            } catch (error) {
                console.error('Duplicate error:', error);
                alert('An error occurred. Please try again.');
            }
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const deleteModal = document.getElementById('deleteModal');
            if (event.target === deleteModal) {
                deleteModal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
