<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>#if(isEdit):Edit#else:Create#endif Assessment - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #E5E7EB;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            margin-right: 12px;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }
        
        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6B7280;
            font-size: 14px;
        }
        
        .nav-breadcrumb a {
            color: #FD8205;
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .btn-primary {
            background: #3B82F6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563EB;
        }
        
        .btn-secondary {
            background: #F3F4F6;
            color: #374151;
        }
        
        .btn-secondary:hover {
            background: #E5E7EB;
        }
        
        .btn-success {
            background: #10B981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 84px);
            transition: grid-template-columns 0.3s ease;
        }

        .main-container.preview-visible {
            grid-template-columns: 300px 1fr 400px;
        }
        
        .sidebar {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow-y: auto;
        }
        
        .sidebar h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 16px;
        }
        
        .builder-area {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow-y: auto;
            position: relative;
        }
        
        .preview-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow-y: auto;
            display: none;
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.3s ease;
        }

        .preview-panel.visible {
            display: block;
            opacity: 1;
            transform: translateX(0);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 6px;
            font-size: 14px;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }
        
        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-checkbox input {
            margin: 0;
        }
        
        .question-types {
            display: grid;
            gap: 8px;
        }
        
        .question-type {
            padding: 12px;
            border: 2px dashed #D1D5DB;
            border-radius: 8px;
            cursor: grab;
            transition: all 0.2s;
            background: #F9FAFB;
        }
        
        .question-type:hover {
            border-color: #3B82F6;
            background: #EFF6FF;
        }
        
        .question-type.dragging {
            opacity: 0.5;
        }
        
        .question-type h4 {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 4px;
        }
        
        .question-type p {
            font-size: 12px;
            color: #6B7280;
        }
        
        .drop-zone {
            min-height: 200px;
            border: 2px dashed #D1D5DB;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            color: #6B7280;
            transition: all 0.2s;
        }
        
        .drop-zone.drag-over {
            border-color: #3B82F6;
            background: #EFF6FF;
        }
        
        .drop-zone.has-content {
            border-style: solid;
            border-color: #E5E7EB;
            background: white;
            text-align: left;
        }
        
        .section-item {
            background: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .section-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            flex: 1;
        }
        
        .section-actions {
            display: flex;
            gap: 8px;
        }
        
        .question-item {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            position: relative;
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .question-type-badge {        
            background-color: #FD8205;
            color: white;
            border-radius: 50px; /* This creates the pill shape */
            padding: 4px 12px;
            font-size: 12px;
            text-decoration: none;
            border: none;
            cursor: pointer;
            margin: 4px;
            transition: all 0.2s ease;
            text-transform: uppercase;
        }
        
        .question-actions {
            display: flex;
            gap: 4px;
        }
        
        .icon-btn {
            background: none;
            border: none;
            padding: 4px;
            border-radius: 4px;
            cursor: pointer;
            color: #6B7280;
            transition: all 0.2s;
        }

        .icon-btn:hover {
            background: #F3F4F6;
            color: #374151;
        }

        .action-btn {
            background: none;
            border: 1px solid #E5E7EB;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
            margin-left: 4px;
        }

        .edit-btn {
            color: #1470C4;
            border-color: #1470C4;
        }

        .edit-btn:hover {
            background: #1470C4;
            color: white;
        }

        .add-btn {
            color: #0ABF89;
            border-color: #0ABF89;
        }

        .add-btn:hover {
            background: #0ABF89;
            color: white;
        }

        .delete-btn {
            color: #E97100;
            border-color: #E97100;
        }

        .delete-btn:hover {
            background: #E97100;
            color: white;
        }
        
        .nested-questions {
            margin-left: 20px;
            margin-top: 12px;
            padding-left: 12px;
            border-left: 2px solid #E5E7EB;
        }

        .nested-questions-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            background: #F9FAFB;
            border: 1px solid #E5E7EB;
            font-size: 12px;
            color: #6B7280;
        }

        .nested-questions-header:hover {
            background: #F3F4F6;
        }

        .collapse-icon {
            font-size: 12px;
            color: #6B7280;
            transition: transform 0.2s ease;
        }

        .collapse-icon.expanded {
            transform: rotate(90deg);
        }

        .nested-questions-content {
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .nested-questions-content.collapsed {
            max-height: 0;
        }

        .nested-questions-count {
            font-size: 11px;
            color: #6B7280;
            background: #E5E7EB;
            padding: 2px 6px;
            border-radius: 10px;
        }
        
        .preview-assessment {
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
            background: #FAFAFA;
        }
        
        .preview-title {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 16px;
        }
        
        .preview-section {
            margin-bottom: 24px;
        }
        
        .preview-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .preview-question {
            margin-bottom: 16px;
            padding: 12px;
            background: white;
            border-radius: 6px;
        }
        
        .preview-question-text {
            font-size: 14px;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .preview-options {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        
        .preview-option {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #6B7280;
        }
        
        .loading {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3B82F6;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background: #FEE2E2;
            color: #DC2626;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
        }
        
        .success-message {
            background: #D1FAE5;
            color: #065F46;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 850px;
            max-width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #E5E7EB;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
        }

        .modal-close {
            color: #6B7280;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
        }

        .modal-close:hover {
            background: #F3F4F6;
            color: #374151;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 20px;
            border-top: 1px solid #E5E7EB;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .options-container {
            #comment: border: 1px solid #E5E7EB; #endcomment
            border-radius: 8px;
            #comment: padding: 16px; #endcomment
            margin-top: 8px;
        }

        .choice-item {
            background: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
        }

        .sub-choice-container {
            margin-top: 12px;
            padding: 8px;
            background: #FFFFFF;
            border: 1px dashed #D1D5DB;
            border-radius: 4px;
        }

        .sub-choice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .sub-choice-title {
            font-size: 12px;
            font-weight: 600;
            color: #6B7280;
        }

        .add-sub-choice-btn {
            background: none;
            border: 1px solid #0ABF89;
            color: #0ABF89;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .add-sub-choice-btn:hover {
            background: #0ABF89;
            color: white;
        }

        .sub-choice-fields {
            display: flex;
            flex-direction: column;
            gap: 12px;
            padding: 16px;
            background: #F3F4F6;
            border-radius: 6px;
            margin-top: 8px;
        }

        .sub-choice-fields > div {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .sub-choice-fields label {
            font-size: 12px;
            color: #6B7280;
            font-weight: 500;
        }

        .sub-choice-input {
            padding: 4px 8px;
            border: 1px solid #D1D5DB;
            border-radius: 3px;
            font-size: 12px;
        }

        .choice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .choice-title {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .choice-remove {
            background: none;
            border: 1px solid #E97100;
            color: #E97100;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .choice-remove:hover {
            background: #E97100;
            color: white;
        }

        .choice-fields {
            display: flex;
            flex-direction: column;
            gap: 12px;
            padding: 16px;
            background: #F9FAFB;
            border-radius: 6px;
            margin-top: 8px;
        }

        .choice-fields > div {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .choice-fields label {
            font-size: 12px;
            color: #6B7280;
            font-weight: 500;
        }

        .choice-input {
            padding: 6px 10px;
            border: 1px solid #D1D5DB;
            border-radius: 4px;
            font-size: 14px;
        }

        .add-option-btn {
            background: #10B981;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 8px;
        }

        .add-option-btn:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">                        
        <h2 style="color: #1F2937;">Assessment Builder</h2>
            <div class="nav-breadcrumb">
                <a href="/admin/dashboard">Admin Dashboard</a>
                <span>›</span>
                <a href="/admin/assessment-creator">Assessment Creator</a>
                <span>›</span>
                <span>#if(isEdit):Edit#else:Create#endif Assessment</span>
            </div>
            
            <div class="header-actions">
                <button onclick="togglePreview()" class="btn btn-secondary" id="previewToggleBtn">Show Preview</button>
                <button onclick="saveAssessment()" class="btn btn-success">Save Assessment</button>
                <a href="/admin/assessment-creator" class="btn btn-secondary">Cancel</a>
            </div>
        </div>
    </div>

    <div class="main-container">
        <!-- Sidebar with Assessment Settings and Question Types -->
        <div class="sidebar">
            <h3>Assessment Settings</h3>
            
            <div class="form-group">
                <label class="form-label">Name *</label>
                <input type="text" id="assessmentName" class="form-input" placeholder="Enter assessment name"
                       value="#if(template):#(template.name)#endif" required>
            </div>

            <div class="form-group" style="display: none;">
                <label class="form-label">Key (Auto-generated)</label>
                <input type="text" id="assessmentKey" class="form-input" readonly>
            </div>
            
            <div class="form-group">
                <label class="form-label">Status</label>
                <select id="assessmentStatus" class="form-select">
                    <option value="draft" #if(template && template.status == "draft"):selected#endif>Draft</option>
                    <option value="active" #if(template && template.status == "active"):selected#endif>Active</option>
                    <option value="archived" #if(template && template.status == "archived"):selected#endif>Archived</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">Language</label>
                <select id="assessmentLanguage" class="form-select">
                    <option value="en-us" #if(template && template.language == "en-us"):selected#endif>English (US)</option>
                    <option value="es" #if(template && template.language == "es"):selected#endif>Spanish</option>
                </select>
            </div>
            
            <div class="form-group">
                <div class="form-checkbox">
                    <input type="checkbox" id="assessmentScored" #if(template && template.scored):checked#endif>
                    <label for="assessmentScored" class="form-label">Scored Assessment</label>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Banner Image URL</label>
                <input type="url" id="assessmentBanner" class="form-input" placeholder="https://example.com/banner.png">
            </div>

            <div class="form-group">
                <label class="form-label">Billing Code (Optional)</label>
                <input type="text" id="assessmentBillingCode" class="form-input" placeholder="Enter billing code">
            </div>

            <hr style="margin: 24px 0; border: none; border-top: 1px solid #E5E7EB;">
            
            <h3>Question Types</h3>
            <div class="question-types">
                <div class="question-type" draggable="true" data-type="single">
                    <h4>📋 Single Choice</h4>
                    <p>Radio button selection</p>
                </div>
                
                <div class="question-type" draggable="true" data-type="multi">
                    <h4>☑️ Multiple Choice</h4>
                    <p>Checkbox selection with sub-questions</p>
                </div>
                
                <div class="question-type" draggable="true" data-type="multiline">
                    <h4>📝 Text Input</h4>
                    <p>Long text responses</p>
                </div>
                
                <div class="question-type" draggable="true" data-type="section">
                    <h4>📂 Section</h4>
                    <p>Group questions into sections</p>
                </div>
            </div>
        </div>

        <!-- Main Builder Area -->
        <div class="builder-area">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <div>Loading...</div>
            </div>
            
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            <div id="successMessage" class="success-message" style="display: none;"></div>                        
            
            <div id="assessmentBuilder" class="drop-zone">
                <div style="text-align: center; color: #6B7280;">
                    <h3 style="margin-top: 50px; margin-bottom: 20px;">Drag question types here to build your assessment</h3>                    
                    <p>Start by dragging a Section from the sidebar to organize your questions</p>
                </div>
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="preview-panel">
            <h3>Live Preview</h3>
            <div id="assessmentPreview" class="preview-assessment">
                <div class="preview-title">Assessment Preview</div>
                <p style="color: #6B7280; text-align: center;">Add questions to see preview</p>
            </div>
        </div>
    </div>

    <!-- Question Edit Modal -->
    <div id="questionEditModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Edit Question</h3>
                <button class="modal-close" onclick="closeQuestionModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="questionEditForm">
                    <div class="form-group">
                        <label class="form-label">Question Text *</label>
                        <textarea id="editQuestionMessage" class="form-input form-textarea" placeholder="Enter the question text that users will see" required></textarea>
                    </div>

                    <div class="form-group" style="display: none;">
                        <label class="form-label">Question Title (Optional)</label>
                        <input type="text" id="editQuestionTitle" class="form-input" placeholder="Internal title for organization">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Value (Optional)</label>
                        <p class="form-help-text" style="color: lightslategray; padding-bottom: 5px; font-size: 14px;"> Value is used for scored assesmnets only. </p>
                        <input type="number" id="editQuestionValue" class="form-input" value="0" min="0">
                    </div>

                    <div class="form-group" style="display: none;">
                        <label class="form-label">Score</label>
                        <input type="number" id="editQuestionScore" class="form-input" value="0" min="0">
                    </div>

                    <div id="questionChoicesContainer" class="form-group" style="display: none;">
                        <label class="form-label">Answer Choices (Nested Questions)</label>
                        <div class="options-container">
                            <div id="choicesList"></div>
                            <button type="button" class="add-option-btn" onclick="addChoice()">+ Add Choice</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeQuestionModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveQuestionChanges()">Save Changes</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>

    <!-- Template data for editing -->
    #if(template):
    <script type="application/json" id="templateData">#unsafeHTML(template.templateJson)</script>
    #endif

    <script>
        // Assessment Builder JavaScript
        let assessmentData = {
            name: '',
            banner: '',
            billingCode: '',
            legend: [],
            sections: []
        };

        let currentSectionId = 0;
        let currentQuestionId = 0;

        // Initialize with existing template data if editing
        #if(template):
        try {
            // Use script tag approach for safer JSON parsing
            const templateDataElement = document.getElementById('templateData');
            if (!templateDataElement) {
                throw new Error('Template data element not found');
            }

            const rawTemplateJson = templateDataElement.textContent || templateDataElement.innerText || '';
            console.log('Raw template JSON:', rawTemplateJson);

            if (!rawTemplateJson.trim()) {
                throw new Error('Empty template JSON');
            }

            // Try to clean up common JSON issues
            let cleanedJson = rawTemplateJson.trim();

            // Check if it looks like valid JSON
            if (!cleanedJson.startsWith('{') && !cleanedJson.startsWith('[')) {
                console.error('JSON does not start with { or [, first char:', cleanedJson.charCodeAt(0));
                throw new Error('JSON does not start with { or [');
            }

            // Try to identify the specific parsing issue
            console.log('Attempting to parse JSON...');
            console.log('JSON starts with:', cleanedJson.substring(0, 50));
            console.log('JSON ends with:', cleanedJson.substring(cleanedJson.length - 50));

            // Try to handle potential double-encoding issues
            let templateData;
            try {
                templateData = JSON.parse(cleanedJson);
            } catch (firstError) {
                console.log('First parse failed, trying to handle double-encoding...');
                // If it's double-encoded as a string, try parsing it as a string first
                try {
                    const stringData = JSON.parse(cleanedJson);
                    if (typeof stringData === 'string') {
                        templateData = JSON.parse(stringData);
                        console.log('Successfully parsed double-encoded JSON');
                    } else {
                        throw firstError;
                    }
                } catch (secondError) {
                    console.error('Both parsing attempts failed');
                    throw firstError;
                }
            }
            console.log('Parsed template data:', templateData);
            assessmentData = templateData;

            // Populate all form fields with existing data
            if (assessmentData.name) {
                document.getElementById('assessmentName').value = assessmentData.name;
                // Auto-generate key from existing name
                const generatedKey = generateKeyFromName(assessmentData.name);
                document.getElementById('assessmentKey').value = generatedKey;
            }

            if (assessmentData.banner) {
                document.getElementById('assessmentBanner').value = assessmentData.banner;
            }

            if (assessmentData.billingCode) {
                document.getElementById('assessmentBillingCode').value = assessmentData.billingCode;
            }

            // Ensure sections array exists
            if (!assessmentData.sections) {
                assessmentData.sections = [];
            }

            // Ensure legend array exists
            if (!assessmentData.legend) {
                assessmentData.legend = [];
            }

            // Normalize data structure and ensure all items have IDs
            normalizeAssessmentData();

            // Update ID counters based on existing data to prevent conflicts
            updateIdCounters();

            updatePreview();
            renderBuilder();
        } catch (e) {
            console.error('Error parsing template data:', e);

            // Get the raw JSON for debugging
            const templateDataElement = document.getElementById('templateData');
            const rawJson = templateDataElement ? (templateDataElement.textContent || templateDataElement.innerText || '') : 'Element not found';
            console.error('Raw JSON that failed to parse:', rawJson);
            console.error('JSON length:', rawJson.length);
            console.error('First 100 characters:', rawJson.substring(0, 100));

            // Try to show user-friendly error with more details
            console.log('Attempting to create basic assessment structure from template name...');

            // Initialize with empty data but try to preserve the name
            assessmentData = {
                name: '#if(template):#(template.name)#endif' || '',
                banner: '',
                billingCode: '',
                legend: [],
                sections: []
            };

            // Populate basic fields from template model
            const nameField = document.getElementById('assessmentName');
            if (nameField && nameField.value) {
                assessmentData.name = nameField.value;
                // Auto-generate key from name
                const generatedKey = generateKeyFromName(nameField.value);
                document.getElementById('assessmentKey').value = generatedKey;
            }

            console.log('Initialized with basic data:', assessmentData);
        }
        #endif

        // Initialize drag and drop functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeDragAndDrop();
            updatePreview();
            setupEventListeners();

            // Add keyboard shortcut for preview toggle (Ctrl/Cmd + P)
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                    e.preventDefault();
                    togglePreview();
                }
            });
        });

        function initializeDragAndDrop() {
            const questionTypes = document.querySelectorAll('.question-type');
            const builderArea = document.getElementById('assessmentBuilder');

            questionTypes.forEach(type => {
                type.addEventListener('dragstart', handleDragStart);
                type.addEventListener('dragend', handleDragEnd);
            });

            builderArea.addEventListener('dragover', handleDragOver);
            builderArea.addEventListener('drop', handleDrop);
            builderArea.addEventListener('dragenter', handleDragEnter);
            builderArea.addEventListener('dragleave', handleDragLeave);
        }

        function handleDragStart(e) {
            e.target.classList.add('dragging');
            e.dataTransfer.setData('text/plain', e.target.dataset.type);
        }

        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDragEnter(e) {
            e.preventDefault();
            e.target.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.target.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.target.classList.remove('drag-over');

            const questionType = e.dataTransfer.getData('text/plain');
            addElement(questionType);
        }

        function addElement(type) {
            const builderArea = document.getElementById('assessmentBuilder');
            builderArea.classList.add('has-content');

            if (type === 'section') {
                addSection();
            } else {
                // If no sections exist, create one first
                if (assessmentData.sections.length === 0) {
                    addSection();
                }
                addQuestion(type, assessmentData.sections.length - 1);
            }

            renderBuilder();
            updatePreview();
        }

        function addSection() {
            const section = {
                id: `section_${currentSectionId++}`,
                type: '',
                title: `Section ${assessmentData.sections.length + 1}`,
                complete: false,
                questions: []
            };

            assessmentData.sections.push(section);
        }

        function addQuestion(type, sectionIndex, parentQuestionIndex = null) {
            const question = {
                type: type,
                level: parentQuestionIndex !== null ? 3 : 1,
                title: `New ${type} question`,
                value: 0,
                message: `New ${type} question`,
                score: 0,
                complete: false,
                questions: []
            };

            // Add default nested questions for choice questions
            if (type === 'single' || type === 'multi') {
                question.questions = [
                    {
                        type: type,
                        level: 3,
                        score: 0,
                        title: "Option 1",
                        value: 0,
                        message: "Option 1",
                        questions: []
                    },
                    {
                        type: type,
                        level: 3,
                        score: 0,
                        title: "Option 2",
                        value: 0,
                        message: "Option 2",
                        questions: []
                    }
                ];
            }

            if (parentQuestionIndex !== null) {
                assessmentData.sections[sectionIndex].questions[parentQuestionIndex].questions.push(question);
            } else {
                assessmentData.sections[sectionIndex].questions.push(question);
            }
        }

        function renderBuilder() {
            const builderArea = document.getElementById('assessmentBuilder');

            if (assessmentData.sections.length === 0) {
                builderArea.innerHTML = `
                    <div style="text-align: center; color: #6B7280;">
                        <h3 style="margin-top: 50px; margin-bottom: 20px;">Drag question types here to build your assessment</h3>                    
                        <p>Start by dragging a Section from the sidebar to organize your questions</p>
                    </div>
                `;
                builderArea.classList.remove('has-content');
                return;
            }

            builderArea.classList.add('has-content');

            let html = '';
            assessmentData.sections.forEach((section, sectionIndex) => {
                html += renderSection(section, sectionIndex);
            });

            builderArea.innerHTML = html;

            // Add event listeners for edit buttons
            addEditEventListeners();
        }

        function renderSection(section, sectionIndex) {
            let html = `
                <div class="section-item" data-section="${sectionIndex}">
                    <div class="section-header">
                        <input type="text" class="section-title form-input" value="${section.title}"
                               onchange="updateSectionTitle(${sectionIndex}, this.value)" placeholder="Section Title">
                        <div class="section-actions">
                            <button class="action-btn add-btn" onclick="addQuestionToSection(${sectionIndex})" title="Add Question">Add Question</button>
                            <button class="action-btn delete-btn" onclick="deleteSection(${sectionIndex})" title="Delete Section">Delete</button>
                        </div>
                    </div>
                    <div class="questions-container">
            `;

            section.questions.forEach((question, questionIndex) => {
                html += renderQuestion(question, sectionIndex, questionIndex);
            });

            if (section.questions.length === 0) {
                html += `
                    <div style="text-align: center; color: #6B7280; padding: 20px; border: 1px dashed #D1D5DB; border-radius: 6px;">
                        <p>No questions in this section</p>
                        <button class="btn btn-secondary" onclick="addQuestionToSection(${sectionIndex})">Add Question</button>
                    </div>
                `;
            }

            html += `
                    </div>
                </div>
            `;

            return html;
        }

        function renderQuestion(question, sectionIndex, questionIndex, isNested = false) {
            const indent = isNested ? 'margin-left: 20px;' : '';

            let html = `
                <div class="question-item" style="${indent}" data-question="${questionIndex}">
                    <div class="question-header">
                    <div class="question-content">
                        <strong>${question.message}</strong>                                                
                    </div>                        
                        <div class="question-actions">
                            <button class="action-btn edit-btn" onclick="editQuestion(${sectionIndex}, '${questionIndex}')" title="Edit">Edit</button>
                            ${question.type === 'multi' ? `<button class="action-btn add-btn" onclick="addSubQuestion(${sectionIndex}, '${questionIndex}')" title="Add Sub-question">Add Sub</button>` : ''}
                            <button class="action-btn delete-btn" onclick="deleteQuestion(${sectionIndex}, '${questionIndex}')" title="Delete">Delete</button>
                        </div>
                    </div>                    
                    <p>${question.type} select</p>
            `;

            if (question.questions && question.questions.length > 0) {
                const nestedId = `nested_${sectionIndex}_${questionIndex}`;
                html += `
                    <div class="nested-questions">
                        <div class="nested-questions-header" onclick="toggleNestedQuestions('${nestedId}')">
                            <span class="collapse-icon">▶</span>
                            <span>Choices/Sub-questions</span>
                            <span class="nested-questions-count">${question.questions.length}</span>
                        </div>
                        <div class="nested-questions-content collapsed" id="${nestedId}">
                `;
                question.questions.forEach((subQuestion, subIndex) => {
                    html += renderQuestion(subQuestion, sectionIndex, `${questionIndex}_${subIndex}`, true);
                });
                html += `
                        </div>
                    </div>
                `;
            }

            html += '</div>';
            return html;
        }

        function generateKeyFromName(name) {
            if (!name) return '';

            return name
                .toLowerCase()                    // Convert to lowercase
                .trim()                          // Remove leading/trailing spaces
                .replace(/[^a-z0-9\s-]/g, '')   // Remove special characters except spaces and hyphens
                .replace(/\s+/g, '_')           // Replace spaces with underscores
                .replace(/-+/g, '_')            // Replace hyphens with underscores
                .replace(/_+/g, '_')            // Replace multiple underscores with single
                .replace(/^_|_$/g, '');         // Remove leading/trailing underscores
        }

        function updateIdCounters() {
            // Reset counters
            currentSectionId = 0;
            currentQuestionId = 0;

            // Update counters based on existing data
            if (assessmentData.sections) {
                assessmentData.sections.forEach(section => {
                    // Extract section ID number if it exists
                    if (section.id && section.id.startsWith('section_')) {
                        const sectionNum = parseInt(section.id.replace('section_', ''));
                        if (!isNaN(sectionNum) && sectionNum >= currentSectionId) {
                            currentSectionId = sectionNum + 1;
                        }
                    }

                    // Update question counters
                    if (section.questions) {
                        updateQuestionCounters(section.questions);
                    }
                });
            }
        }

        function updateQuestionCounters(questions) {
            questions.forEach(question => {
                // Extract question ID number if it exists
                if (question.id && question.id.startsWith('question_')) {
                    const questionNum = parseInt(question.id.replace('question_', ''));
                    if (!isNaN(questionNum) && questionNum >= currentQuestionId) {
                        currentQuestionId = questionNum + 1;
                    }
                }

                // Recursively check nested questions
                if (question.questions && question.questions.length > 0) {
                    updateQuestionCounters(question.questions);
                }
            });
        }

        function normalizeAssessmentData() {
            // Ensure all sections have IDs
            if (assessmentData.sections) {
                assessmentData.sections.forEach((section, index) => {
                    if (!section.id) {
                        section.id = `section_${index}`;
                    }

                    // Ensure section has required properties
                    if (!section.type) section.type = '';
                    if (!section.title) section.title = `Section ${index + 1}`;
                    if (section.complete === undefined) section.complete = false;
                    if (!section.questions) section.questions = [];

                    // Normalize questions
                    normalizeQuestions(section.questions);
                });
            }
        }

        function normalizeQuestions(questions) {
            questions.forEach((question, index) => {
                // Ensure question has ID
                if (!question.id) {
                    question.id = `question_${currentQuestionId++}`;
                }

                // Ensure question has required properties
                if (!question.type) question.type = 'multiline';
                if (!question.title) question.title = question.message || '';
                if (!question.message) question.message = question.title || '';
                if (question.level === undefined) question.level = 1;
                if (question.value === undefined) question.value = 0;
                if (question.score === undefined) question.score = 0;
                if (!question.questions) question.questions = [];

                // Recursively normalize nested questions
                if (question.questions.length > 0) {
                    normalizeQuestions(question.questions);
                }
            });
        }

        function setupEventListeners() {
            // Assessment settings listeners
            document.getElementById('assessmentName').addEventListener('input', function() {
                assessmentData.name = this.value;

                // Auto-generate key from name
                const generatedKey = generateKeyFromName(this.value);
                document.getElementById('assessmentKey').value = generatedKey;

                updatePreview();
            });

            document.getElementById('assessmentBanner').addEventListener('input', function() {
                assessmentData.banner = this.value;
                updatePreview();
            });

            document.getElementById('assessmentBillingCode').addEventListener('input', function() {
                assessmentData.billingCode = this.value;
                updatePreview();
            });
        }

        function updateSectionTitle(sectionIndex, title) {
            assessmentData.sections[sectionIndex].title = title;
            updatePreview();
        }

        function addQuestionToSection(sectionIndex) {
            const questionType = prompt('Question type (single, multi, multiline):');
            if (questionType && ['single', 'multi', 'multiline'].includes(questionType)) {
                addQuestion(questionType, sectionIndex);
                renderBuilder();
                updatePreview();
            }
        }

        function addSubQuestion(sectionIndex, questionIndex) {
            const questionType = prompt('Sub-question type (single, multi, multiline):');
            if (questionType && ['single', 'multi', 'multiline'].includes(questionType)) {
                addQuestion(questionType, sectionIndex, questionIndex);
                renderBuilder();
                updatePreview();
            }
        }

        function deleteSection(sectionIndex) {
            if (confirm('Delete this section and all its questions?')) {
                assessmentData.sections.splice(sectionIndex, 1);
                renderBuilder();
                updatePreview();
            }
        }

        function deleteQuestion(sectionIndex, questionIndex) {
            if (confirm('Delete this question?')) {
                if (typeof questionIndex === 'string' && questionIndex.includes('_')) {
                    // Handle nested question deletion
                    const [parentIndex, subIndex] = questionIndex.split('_').map(Number);
                    assessmentData.sections[sectionIndex].questions[parentIndex].questions.splice(subIndex, 1);
                } else {
                    assessmentData.sections[sectionIndex].questions.splice(questionIndex, 1);
                }
                renderBuilder();
                updatePreview();
            }
        }

        let currentEditingQuestion = null;
        let currentEditingSectionIndex = null;
        let currentEditingQuestionIndex = null;

        function editQuestion(sectionIndex, questionIndex) {
            let question;
            console.log('Editing question', sectionIndex, questionIndex);
            if (typeof questionIndex === 'string' && questionIndex.includes('_')) {
                const [parentIndex, subIndex] = questionIndex.split('_').map(Number);
                question = assessmentData.sections[sectionIndex].questions[parentIndex].questions[subIndex];
            } else {
                question = assessmentData.sections[sectionIndex].questions[questionIndex];
            }

            // Store current editing context
            currentEditingQuestion = question;
            currentEditingSectionIndex = sectionIndex;
            currentEditingQuestionIndex = questionIndex;

            // Populate modal with current values
            document.getElementById('editQuestionMessage').value = question.message || '';
            document.getElementById('editQuestionTitle').value = question.title || '';
            document.getElementById('editQuestionValue').value = question.value || 0;
            document.getElementById('editQuestionScore').value = question.score || 0;

            // Show/hide choices based on question type
            const choicesContainer = document.getElementById('questionChoicesContainer');
            if (question.type === 'single' || question.type === 'multi') {
                choicesContainer.style.display = 'block';
                populateChoices(question.questions || []);
            } else {
                choicesContainer.style.display = 'none';
            }

            // Show modal
            document.getElementById('questionEditModal').style.display = 'block';
        }

        function populateChoices(choices) {
            const choicesList = document.getElementById('choicesList');
            choicesList.innerHTML = '';

            choices.forEach((choice, index) => {
                const choiceDiv = document.createElement('div');
                choiceDiv.className = 'choice-item';
                choiceDiv.innerHTML = `
                    <div class="choice-header">
                        <span class="choice-title">Choice ${index + 1}</span>
                        <div style="display: flex; gap: 8px;">
                            <button type="button" class="add-sub-choice-btn" onclick="toggleSubChoice(${index})">
                                ${choice.questions && choice.questions.length > 0 ? 'Edit' : 'Add'} Sub-Question
                            </button>
                            <button type="button" class="choice-remove" onclick="removeChoice(${index})">Remove</button>
                        </div>
                    </div>
                    <div class="choice-fields">
                        <div>
                            <label style="font-size: 12px; color: #6B7280;">Choice Text</label>
                            <input type="text" class="choice-input choice-message" value="${choice.message || choice.title || ''}" placeholder="Choice text">
                        </div>
                        <div>
                            <label style="font-size: 12px; color: #6B7280;">Type</label>
                            <select class="choice-input choice-type">
                                <option value="single" ${choice.type === 'single' ? 'selected' : ''}>Single</option>
                                <option value="multi" ${choice.type === 'multi' ? 'selected' : ''}>Multi</option>
                                <option value="multiline" ${choice.type === 'multiline' ? 'selected' : ''}>Text</option>
                            </select>
                        </div>
                        <div>
                            <label style="font-size: 12px; color: #6B7280;">Value (Used for scored assesmnets only.) </label>                            
                            <input type="number" class="choice-input choice-value" value="${choice.value || 0}" min="0">
                        </div>
                        <div style="display: none;">
                            <label style="font-size: 12px; color: #6B7280;">Score</label>
                            <input type="number" class="choice-input choice-score" value="${choice.score || 0}" min="0">
                        </div>
                    </div>
                    <div class="sub-choice-container" id="subChoice_${index}">
                        <div class="sub-choice-content" style="display: ${choice.questions && choice.questions.length > 0 ? 'block' : 'none'};">
                            ${choice.questions && choice.questions.length > 0 ? renderSubChoice(choice.questions[0], index) : ''}
                        </div>
                    </div>
                `;
                choicesList.appendChild(choiceDiv);
            });
        }

        function addChoice() {
            const choicesList = document.getElementById('choicesList');
            const choiceCount = choicesList.children.length;

            const choiceDiv = document.createElement('div');
            choiceDiv.className = 'choice-item';
            choiceDiv.innerHTML = `
                <div class="choice-header">
                    <span class="choice-title">Choice ${choiceCount + 1}</span>
                    <div style="display: flex; gap: 8px;">
                        <button type="button" class="add-sub-choice-btn" onclick="toggleSubChoice(${choiceCount})">Add Sub-Question</button>
                        <button type="button" class="choice-remove" onclick="removeChoice(${choiceCount})">Remove</button>
                    </div>
                </div>
                <div class="choice-fields">
                    <div>
                        <label style="font-size: 12px; color: #6B7280;">Choice Text</label>
                        <input type="text" class="choice-input choice-message" value="Option ${choiceCount + 1}" placeholder="Choice text">
                    </div>
                    <div>
                        <label style="font-size: 12px; color: #6B7280;">Type</label>
                        <select class="choice-input choice-type">
                            <option value="single">Single</option>
                            <option value="multi" selected>Multi</option>
                            <option value="multiline">Text</option>
                        </select>
                    </div>
                    <div>
                        <label style="font-size: 12px; color: #6B7280;">Value</label>
                        <input type="number" class="choice-input choice-value" value="0" min="0">
                    </div>
                    <div style="display: none;">
                        <label style="font-size: 12px; color: #6B7280;">Score</label>
                        <input type="number" class="choice-input choice-score" value="0" min="0">
                    </div>
                </div>
                <div class="sub-choice-container" id="subChoice_${choiceCount}">
                    <div class="sub-choice-content" style="display: none;"></div>
                </div>
            `;
            choicesList.appendChild(choiceDiv);
        }

        function removeChoice(index) {
            const choicesList = document.getElementById('choicesList');
            if (choicesList.children.length > 1) {
                choicesList.children[index].remove();
                // Re-index the remaining choices
                Array.from(choicesList.children).forEach((child, newIndex) => {
                    const removeBtn = child.querySelector('.choice-remove');
                    removeBtn.onclick = () => removeChoice(newIndex);
                    child.querySelector('.choice-title').textContent = `Choice ${newIndex + 1}`;
                });
            }
        }

        function renderSubChoice(subChoice, parentIndex) {
            return `
                <div class="sub-choice-fields">
                    <div>
                        <label style="font-size: 11px; color: #6B7280;">Sub-Question Text</label>
                        <input type="text" class="sub-choice-input sub-choice-message" value="${subChoice.message || subChoice.title || ''}" placeholder="Follow-up question">
                    </div>
                    <div>
                        <label style="font-size: 11px; color: #6B7280;">Type</label>
                        <select class="sub-choice-input sub-choice-type">
                            <option value="single" ${subChoice.type === 'single' ? 'selected' : ''}>Single</option>
                            <option value="multi" ${subChoice.type === 'multi' ? 'selected' : ''}>Multi</option>
                            <option value="multiline" ${subChoice.type === 'multiline' ? 'selected' : ''}>Text</option>
                        </select>
                    </div>
                    <div>
                        <button type="button" class="choice-remove" onclick="removeSubChoice(${parentIndex})" style="margin-top: 16px;">Remove</button>
                    </div>
                </div>
            `;
        }

        function toggleSubChoice(choiceIndex) {
            const subChoiceContainer = document.getElementById(`subChoice_${choiceIndex}`);
            const subChoiceContent = subChoiceContainer.querySelector('.sub-choice-content');
            const toggleBtn = subChoiceContainer.querySelector('.add-sub-choice-btn');

            if (subChoiceContent.style.display === 'none') {
                // Show sub-choice editor
                subChoiceContent.style.display = 'block';
                subChoiceContent.innerHTML = renderSubChoice({
                    message: 'Please specify',
                    type: 'multiline'
                }, choiceIndex);
                toggleBtn.textContent = 'Remove Sub-Question';
            } else {
                // Hide sub-choice editor
                subChoiceContent.style.display = 'none';
                subChoiceContent.innerHTML = '';
                toggleBtn.textContent = 'Add Sub-Question';
            }
        }

        function removeSubChoice(choiceIndex) {
            const subChoiceContainer = document.getElementById(`subChoice_${choiceIndex}`);
            const subChoiceContent = subChoiceContainer.querySelector('.sub-choice-content');
            const toggleBtn = subChoiceContainer.querySelector('.add-sub-choice-btn');

            subChoiceContent.style.display = 'none';
            subChoiceContent.innerHTML = '';
            toggleBtn.textContent = 'Add Sub-Question';
        }

        function toggleNestedQuestions(nestedId) {
            const content = document.getElementById(nestedId);
            const header = content.previousElementSibling;
            const icon = header.querySelector('.collapse-icon');

            if (content.classList.contains('collapsed')) {
                // Expand
                content.classList.remove('collapsed');
                content.style.maxHeight = content.scrollHeight + 'px';
                icon.classList.add('expanded');
            } else {
                // Collapse
                content.classList.add('collapsed');
                content.style.maxHeight = '0';
                icon.classList.remove('expanded');
            }
        }

        function closeQuestionModal() {
            document.getElementById('questionEditModal').style.display = 'none';
            currentEditingQuestion = null;
            currentEditingSectionIndex = null;
            currentEditingQuestionIndex = null;
        }

        function saveQuestionChanges() {
            if (!currentEditingQuestion) return;

            // Get form values
            const message = document.getElementById('editQuestionMessage').value.trim();
            const title = document.getElementById('editQuestionMessage').value.trim();
            const value = parseInt(document.getElementById('editQuestionValue').value) || 0;
            const score = parseInt(document.getElementById('editQuestionScore').value) || 0;

            if (!message) {
                alert('Question text is required');
                return;
            }

            // Update question
            currentEditingQuestion.message = message;
            currentEditingQuestion.title = title;
            currentEditingQuestion.value = value;
            currentEditingQuestion.score = score;

            // Update nested questions (choices) if applicable
            if (currentEditingQuestion.type === 'single' || currentEditingQuestion.type === 'multi') {
                const choiceItems = document.querySelectorAll('#choicesList .choice-item');
                const choices = Array.from(choiceItems).map((item, index) => {
                    const message = item.querySelector('.choice-message').value.trim();
                    const type = item.querySelector('.choice-type').value;
                    const value = parseInt(item.querySelector('.choice-value').value) || 0;
                    const score = parseInt(item.querySelector('.choice-score').value) || 0;

                    // Check for sub-choice
                    const subChoiceContainer = document.getElementById(`subChoice_${index}`);
                    const subChoiceContent = subChoiceContainer.querySelector('.sub-choice-content');
                    let subQuestions = [];

                    if (subChoiceContent.style.display !== 'none' && subChoiceContent.innerHTML.trim()) {
                        const subMessage = subChoiceContent.querySelector('.sub-choice-message');
                        const subType = subChoiceContent.querySelector('.sub-choice-type');

                        if (subMessage && subType && subMessage.value.trim()) {
                            subQuestions = [{
                                type: subType.value,
                                level: 4,
                                score: 0,
                                title: subMessage.value.trim(),
                                value: 0,
                                message: subMessage.value.trim(),
                                questions: []
                            }];
                        }
                    }

                    return {
                        type: type,
                        level: 3,
                        score: score,
                        title: message,
                        value: value,
                        message: message,
                        questions: subQuestions
                    };
                }).filter(choice => choice.message);

                currentEditingQuestion.questions = choices.length > 0 ? choices : [
                    {
                        type: currentEditingQuestion.type,
                        level: 3,
                        score: 0,
                        title: "Option 1",
                        value: 0,
                        message: "Option 1",
                        questions: []
                    },
                    {
                        type: currentEditingQuestion.type,
                        level: 3,
                        score: 0,
                        title: "Option 2",
                        value: 0,
                        message: "Option 2",
                        questions: []
                    }
                ];
            }

            // Close modal and update UI
            closeQuestionModal();
            renderBuilder();
            updatePreview();
        }

        function addEditEventListeners() {
            // Event listeners are added inline in the HTML for simplicity
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('questionEditModal');
            if (event.target === modal) {
                closeQuestionModal();
            }
        }

        function updatePreview() {
            const previewArea = document.getElementById('assessmentPreview');

            if (assessmentData.sections.length === 0) {
                previewArea.innerHTML = `
                    <div class="preview-title">Assessment Preview</div>
                    <p style="color: #6B7280; text-align: center;">Add questions to see preview</p>
                `;
                return;
            }

            let html = `<div class="preview-title">${assessmentData.name || 'Untitled Assessment'}</div>`;

            if (assessmentData.banner) {
                html += `<img src="${assessmentData.banner}" alt="Assessment Banner" style="width: 100%; max-height: 100px; object-fit: cover; border-radius: 6px; margin-bottom: 16px;">`;
            }

            assessmentData.sections.forEach(section => {
                html += `
                    <div class="preview-section">
                        <div class="preview-section-title">${section.title}</div>
                `;

                section.questions.forEach(question => {
                    html += renderPreviewQuestion(question);
                });

                html += '</div>';
            });

            previewArea.innerHTML = html;
        }

        function renderPreviewQuestion(question, isNested = false) {
            const indent = isNested ? 'margin-left: 16px;' : '';

            let html = `
                <div class="preview-question" style="${indent}">
                    <div class="preview-question-text">${question.message}</div>
            `;

            if (question.type === 'single') {
                const choices = question.questions || [];
                html += '<div class="preview-options">';
                choices.forEach(choice => {
                    html += `
                        <div class="preview-option">
                            <input type="radio" disabled> ${choice.message || choice.title}
                        </div>
                    `;
                });
                html += '</div>';
            } else if (question.type === 'multi') {
                const choices = question.questions || [];
                html += '<div class="preview-options">';
                choices.forEach(choice => {
                    html += `
                        <div class="preview-option">
                            <input type="checkbox" disabled> ${choice.message || choice.title}
                        </div>
                    `;
                });
                html += '</div>';
            } else if (question.type === 'multiline') {
                html += `<textarea disabled placeholder="Text response..." style="width: 100%; min-height: 60px; padding: 8px; border: 1px solid #D1D5DB; border-radius: 4px;"></textarea>`;
            }

            if (question.questions && question.questions.length > 0) {
                question.questions.forEach(subQuestion => {
                    html += renderPreviewQuestion(subQuestion, true);
                });
            }

            html += '</div>';
            return html;
        }

        function togglePreview() {
            const mainContainer = document.querySelector('.main-container');
            const previewPanel = document.querySelector('.preview-panel');
            const toggleBtn = document.getElementById('previewToggleBtn');

            if (previewPanel.classList.contains('visible')) {
                // Hide preview
                previewPanel.classList.remove('visible');
                mainContainer.classList.remove('preview-visible');
                toggleBtn.textContent = '👁️ Show Preview';
            } else {
                // Show preview
                previewPanel.classList.add('visible');
                mainContainer.classList.add('preview-visible');
                toggleBtn.textContent = '👁️ Hide Preview';
                // Update preview content when showing
                updatePreview();
            }
        }

        function previewAssessment() {
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            const previewContent = document.getElementById('assessmentPreview').innerHTML;

            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Assessment Preview</title>
                    <style>
                        body { font-family: 'Graphik', sans-serif; padding: 20px; background: #F5F5F5; }
                        .preview-assessment { background: white; padding: 20px; border-radius: 8px; max-width: 600px; margin: 0 auto; }
                        .preview-title { font-size: 24px; font-weight: 600; margin-bottom: 20px; }
                        .preview-section { margin-bottom: 24px; }
                        .preview-section-title { font-size: 18px; font-weight: 600; margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #E5E7EB; }
                        .preview-question { margin-bottom: 16px; padding: 12px; background: #F9FAFB; border-radius: 6px; }
                        .preview-question-text { font-weight: 500; margin-bottom: 8px; }
                        .preview-options { display: flex; flex-direction: column; gap: 6px; }
                        .preview-option { display: flex; align-items: center; gap: 8px; }
                    </style>
                </head>
                <body>
                    <div class="preview-assessment">
                        ${previewContent}
                    </div>
                </body>
                </html>
            `);
            previewWindow.document.close();
        }

        async function saveAssessment() {
            const loadingEl = document.getElementById('loading');
            const errorEl = document.getElementById('errorMessage');
            const successEl = document.getElementById('successMessage');

            // Hide previous messages
            errorEl.style.display = 'none';
            successEl.style.display = 'none';

            // Validate required fields
            const name = document.getElementById('assessmentName').value.trim();
            const key = document.getElementById('assessmentKey').value.trim();

            if (!name) {
                showError('Assessment name is required');
                return;
            }

            if (!key) {
                showError('Assessment key is required');
                return;
            }

            if (assessmentData.sections.length === 0) {
                showError('Assessment must have at least one section');
                return;
            }

            // Update assessment data with form values
            assessmentData.name = name;

            const templateData = {
                name: name,
                key: key,
                status: document.getElementById('assessmentStatus').value,
                language: document.getElementById('assessmentLanguage').value,
                scored: document.getElementById('assessmentScored').checked,
                templateJson: JSON.stringify(assessmentData, null, 2)
            };

            loadingEl.style.display = 'block';

            try {
                const isEdit = #if(isEdit):true#else:false#endif;
                const url = isEdit ? `/admin/assessment-creator/#if(template):#(template.id)#endif` : '/admin/assessment-creator/create';
                const method = isEdit ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(templateData)
                });

                if (response.ok) {
                    showSuccess('Assessment saved successfully!');
                    setTimeout(() => {
                        window.location.href = '/admin/assessment-creator';
                    }, 1500);
                } else {
                    const errorText = await response.text();
                    showError(`Failed to save assessment: ${errorText}`);
                }
            } catch (error) {
                console.error('Save error:', error);
                showError('An error occurred while saving. Please try again.');
            } finally {
                loadingEl.style.display = 'none';
            }
        }

        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            errorEl.scrollIntoView({ behavior: 'smooth' });
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            successEl.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
