<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consent Creator Dashboard - Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Graphik:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            color: #1F2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #E5E7EB;
            padding: 16px 24px;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6B7280;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: #3B82F6;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #FD8205;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563EB;
        }
        
        .btn-secondary {
            background: #F3F4F6;
            color: #374151;
            border: 1px solid #D1D5DB;
        }
        
        .btn-secondary:hover {
            background: #E5E7EB;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            color: #6B7280;
            margin-bottom: 32px;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
        }
        
        .template-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #E5E7EB;
            transition: all 0.2s;
        }
        
        .template-card:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-color: #D1D5DB;
        }
        
        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }
        
        .template-title {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 4px;
        }
        
        .template-key {
            font-size: 12px;
            color: #6B7280;
            font-family: 'Monaco', monospace;
            background: #F3F4F6;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .template-meta {
            display: flex;
            flex-direction: column;
            gap: 0.25em;            
            margin-bottom: 16px;
            font-size: 14px;
            color: #6B7280;
        }
        
        .template-status {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-active .status-dot {
            background: #10B981;
        }
        
        .status-draft .status-dot {
            background: #F59E0B;
        }
        
        .status-inactive .status-dot {
            background: #EF4444;
        }
        
        .template-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .btn-danger {
            background: #EF4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #DC2626;
        }
        
        .empty-state {
            text-align: center;
            padding: 64px 24px;
            background: white;
            border-radius: 12px;
            border: 2px dashed #D1D5DB;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .empty-title {
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .empty-description {
            color: #6B7280;
            margin-bottom: 24px;
        }
        
        .user-info {
            font-size: 14px;
            color: #6B7280;
        }
        
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .loading-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            text-align: center;
        }

     .consent-header {
  display: flex;
  justify-content: space-between; /* space text on left, button on right */
  align-items: center;            /* vertical alignment */
  gap: 0.25em;                      /* spacing between columns */
  margin-bottom: 2rem;
}

.page-title {
  margin: 0;
  padding-bottom: 0.25em;
}

.page-subtitle {
  margin: 0;
}

.consent-action .btn {
  white-space: nowrap; /* prevents button text from breaking into two lines */
}
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading" class="loading">
        <div class="loading-content">
            <div>Processing...</div>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="breadcrumb">
                <a href="/admin/dashboard">Admin Dashboard</a>
                <span>›</span>
                <span>Consent Creator</span>
            </div>
            
            <div class="header-actions">
                <div class="user-info">Welcome, #(adminUsername)</div>                
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-container">
    <div>
        <div class="consent-header">
  <div class="consent-text">
    <h1 class="page-title">Consent Creator</h1>
    <p class="page-subtitle">Create and manage consent forms for your organization</p>
  </div>
  <div class="consent-action">
    <a href="/admin/consent-creator/create" class="btn btn-primary">Create New Consent Form</a>
  </div>
</div>

        #if(count(templates) > 0):
        <div class="templates-grid">
            #for(template in templates):
            <div class="template-card">
                <div class="template-header">
                    <div>
                        <div class="template-title">#(template.name)</div>                        
                    </div>
                </div>
                
                <div class="template-meta">
                    <div class="template-status status-#(template.status)">                        
                        Status: #(template.status)
                    </div>
                    <div>Language: #(template.language)</div>
                </div>
                
                <div class="template-actions">
                    <a href="/admin/consent-creator/#(template.id)/edit" class="btn btn-secondary btn-small">Edit</a>
                    <a href="/admin/consent-creator/#(template.id)/edit" class="btn btn-secondary btn-small">View</a>
                    <button onclick="duplicateTemplate('#(template.id)')" class="btn btn-secondary btn-small">Duplicate</button>
                    <button onclick="deleteTemplate('#(template.id)', '#(template.name)')" class="btn btn-danger btn-small">Delete</button>
                </div>
            </div>
            #endfor
        </div>
        #else:
        <div class="empty-state">
            <div class="empty-icon">📋</div>
            <h2 class="empty-title">No Consent Forms Yet</h2>
            <p class="empty-description">Create your first consent form to get started with collecting digital consent from participants.</p>
            <a href="/admin/consent-creator/create" class="btn btn-primary">📝 Create Your First Consent Form</a>
        </div>
        #endif
    </div>

    <script>
        function previewTemplate(templateId) {
            window.open(`/admin/consent-creator/${templateId}/preview`, '_blank', 'width=800,height=600');
        }
        
        function duplicateTemplate(templateId) {
            if (confirm('Create a copy of this consent form?')) {
                showLoading();
                fetch(`/admin/consent-creator/${templateId}/duplicate`, {
                    method: 'POST'
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Error duplicating template');
                        hideLoading();
                    }
                })
                .catch(error => {
                    alert('Error duplicating template');
                    hideLoading();
                });
            }
        }
        
        function deleteTemplate(templateId, templateName) {
            if (confirm(`Are you sure you want to delete "${templateName}"? This action cannot be undone.`)) {
                showLoading();
                fetch(`/admin/consent-creator/${templateId}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Error deleting template');
                        hideLoading();
                    }
                })
                .catch(error => {
                    alert('Error deleting template');
                    hideLoading();
                });
            }
        }
        
        function showLoading() {
            document.getElementById('loading').style.display = 'flex';
        }
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
    </script>
</body>
</html>
