<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>#if(isEdit):Edit#else:Create#endif Program - #(orgName) - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #E5E7EB;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-info {
            color: #6B7280;
            font-size: 14px;
        }
        
        .logout-btn {
            background: #EF4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .logout-btn:hover {
            background: #DC2626;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: #3B82F6;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .breadcrumb-separator {
            color: #9CA3AF;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            color: #6B7280;
            font-size: 16px;
            margin-bottom: 40px;
        }
        
        .form-container {
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #E5E7EB;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            font-size: 14px;            
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Monaco', 'Menlo', monospace;
            resize: vertical;
            min-height: 400px;
            transition: border-color 0.2s;
        }
        
        .form-textarea:focus {
            outline: none;
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-help {
            font-size: 12px;
            color: #6B7280;
            margin-top: 4px;
        }       
        
        .error-message {
            background: #FEF2F2;
            border: 1px solid #FECACA;
            color: #DC2626;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-size: 14px;
        }
        
        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #E5E7EB;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: none;
            border: 1px solid #1470C4;
            color: #1470C4;
        }

        .btn-primary:hover {
            background: #1470C4;
            color: white;
        }

        .btn-secondary {
            background: none;
            border: 1px solid #6B7280;
            color: #6B7280;
        }

        .btn-secondary:hover {
            background: #6B7280;
            color: white;
        }
        
        .json-example {
            background: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
            margin-top: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            color: #374151;
            white-space: pre-wrap;
        }

        .config-section {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .config-section h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 16px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-header h3 {
            margin-bottom: 0;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .dynamic-list {
            margin-bottom: 16px;
        }

        .dynamic-item {
            background: #F8F9FA;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            position: relative;
        }

        .dynamic-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .dynamic-item-title {
            font-weight: 500;
            color: #374151;
        }

        .dynamic-item-fields {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 12px;
            align-items: end;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 14px;
            border-radius: 6px;
        }

        .btn-danger {
            background: none;
            border: 1px solid #E97100;
            color: #E97100;
        }

        .btn-danger:hover {
            background: #E97100;
            color: white;
        }

        .checkbox-group {
            margin-top: 16px;
        }

        .checkbox-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 0;
        }

        .checkbox-wrapper input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin: 0;
            cursor: pointer;
        }

        .checkbox-label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            cursor: pointer;
            margin: 0;
        }

        .full-width-select {
            width: 100%;
            padding: 12px 16px;
            margin-bottom: 12px;
        }

        .checkbox-wrapper-inline {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 0;
        }

        .checkbox-wrapper-inline input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin: 0;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-text">Wellup</div>
            </div>
            
            <div class="user-menu">
                <span class="user-info">Welcome, #(adminUsername)</span>
                <form method="POST" action="/admin/logout" style="display: inline;">
                    <button type="submit" class="logout-btn">Logout</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="breadcrumb">
            <a href="/admin/dashboard">Admin Dashboard</a>
            <span class="breadcrumb-separator">›</span>
            <a href="/admin/org-programs">Organization Program Management</a>
            <span class="breadcrumb-separator">›</span>
            <a href="/admin/org-programs/#(orgID)">#(orgName)</a>
            <span class="breadcrumb-separator">›</span>
            <span>#if(isEdit):Edit Program#else:Create Program#endif</span>
        </div>
        
        <h1 class="page-title">#if(isEdit):Edit Program#else:Create New Program#endif</h1>
        <p class="page-subtitle">Configure program settings, assessments, and review requirements for #(orgName)</p>
        
        <div class="form-container">
            #if(error):
            <div class="error-message">
                #(error)
            </div>
            #endif
            
            <form method="POST" action="#if(isEdit):/admin/org-programs/#(orgID)/#(program.programKey)#else:/admin/org-programs/#(orgID)/create#endif">
                #if(isEdit):
                <input type="hidden" name="_method" value="PUT">
                #endif
                
                <div class="form-group">
                    <label for="programName" class="form-label">Program Name *</label>
                    <input
                        type="text"
                        id="programName"
                        name="programName"
                        class="form-input"
                        value="#if(program):#(program.programName)#endif"
                        required
                        placeholder="e.g., Long-Term Services and Supports"
                        oninput="generateProgramKey()"
                    >
                    <div class="form-help">A descriptive name for this program</div>
                </div>
                
                #if(!isEdit):
                <div class="form-group">
                    <label for="programKey" class="form-label">Program Key *</label>
                    <input
                        type="text"
                        id="programKey"
                        name="programKey"
                        class="form-input"
                        required
                        placeholder="e.g., ltss"
                        pattern="[a-z0-9_]+"
                        title="Only lowercase letters, numbers, and underscores allowed"
                        readonly
                    >
                    <div class="form-help">Unique identifier for this program (auto-generated from name)</div>
                </div>
                #endif
                
                <!-- Hidden field for the final JSON configuration -->
                <input type="hidden" id="programConfig" name="programConfig" required>

                <!-- Program Configuration Form Fields -->
                <div class="config-section">
                    <h3>Program Configuration</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="templateId" class="form-label">Template ID *</label>
                            <input
                                type="text"
                                id="templateId"
                                name="templateId"
                                class="form-input"
                                required
                                placeholder="e.g., program_template_001"
                            >
                            <div class="form-help">Unique identifier for the program template</div>
                        </div>

                        <div class="form-group">
                            <label for="programType" class="form-label">Program Type *</label>
                            <select id="programType" name="programType" class="form-input" required>
                                <option value="">Select Program Type</option>
                                <option value="LTSS">Long-Term Services and Supports (LTSS)</option>
                                <option value="BEHAVIORAL">Behavioral Health</option>
                                <option value="MEDICAL">Medical Management</option>
                                <option value="WELLNESS">Wellness Program</option>
                                <option value="CUSTOM">Custom Program</option>
                            </select>
                            <div class="form-help">Category of program being created</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="displayName" class="form-label">Display Name *</label>
                        <input
                            type="text"
                            id="displayName"
                            name="displayName"
                            class="form-input"
                            required
                            placeholder="e.g., Long-Term Services and Supports"
                        >
                        <div class="form-help">User-friendly name displayed in the interface</div>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">Description *</label>
                        <textarea
                            id="description"
                            name="description"
                            class="form-textarea"
                            required
                            placeholder="Detailed description of the program..."
                            rows="3"
                        ></textarea>
                        <div class="form-help">Comprehensive description of the program's purpose and scope</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="reviewFrequencyDays" class="form-label">Review Frequency (Days) *</label>
                            <input
                                type="number"
                                id="reviewFrequencyDays"
                                name="reviewFrequencyDays"
                                class="form-input"
                                required
                                min="1"
                                placeholder="90"
                            >
                            <div class="form-help">How often reviews should occur (in days)</div>
                        </div>

                        <div class="form-group">
                            <label for="daysToCompleteAssessment" class="form-label">Assessment Completion Days *</label>
                            <input
                                type="number"
                                id="daysToCompleteAssessment"
                                name="daysToCompleteAssessment"
                                class="form-input"
                                required
                                min="1"
                                placeholder="30"
                            >
                            <div class="form-help">Days allowed to complete assessments</div>
                        </div>
                    </div>
                </div>

                <!-- Required Assessments Section -->
                <div class="config-section">
                    <div class="section-header">
                    <div>
                    <h3>Required Assessments</h3>
                    <p style="font-size: 14px; color: #6B7280; margin-top: 8px;">
                        Key health or risk assessments that must be completed for each care cycle to meet program goals and stay NCQA-compliant.
                        </p>
                    </div>                            
                        <button type="button" class="btn btn-secondary btn-small" onclick="addAssessment()">
                            + Add Assessment
                        </button>
                    </div>
                    <div id="assessmentsList" class="dynamic-list">
                        <!-- Assessment items will be added here dynamically -->
                    </div>
                    <div class="form-help">Define assessments that must be completed as part of this program</div>
                </div>

                <!-- Program Reviews Section -->
                <div class="config-section">
                    <div class="section-header">
                    <div>
                    <h3>Program Reviews</h3> 
                    <p style="font-size: 14px; color: #6B7280; margin-top: 8px;">
                        Scheduled checkpoints to review and update a member’s care management program in line with NCQA requirements.
                        </p>
                    </div>                        
                        <button type="button" class="btn btn-secondary btn-small" onclick="addReview()">
                            + Add Review
                        </button>                         
                    </div>                    
                    <div id="reviewsList" class="dynamic-list">
                        <!-- Review items will be added here dynamically -->
                    </div>
                    <div class="form-help">Define review processes that are part of this program</div>
                </div>

                <div class="form-actions">
                    <a href="/admin/org-programs/#(orgID)" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        #if(isEdit):Update Program#else:Create Program#endif
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Template data for editing -->
    #if(program):
    <script type="application/json" id="programData">#unsafeHTML(program.programConfig)</script>
    #endif

    <script>
        // Program configuration data
        let programData = {
            orgId: '#(orgID)',
            templateId: '',
            programType: '',
            displayName: '',
            description: '',
            reviewFrequencyDays: 90,
            daysToCompleteAssessment: 30,
            requiredAssessments: [],
            programReviews: []
        };

        let assessmentCounter = 0;
        let reviewCounter = 0;

        // Initialize form
        document.addEventListener('DOMContentLoaded', function() {
            // Load existing data if editing
            #if(program):
            try {
                const existingData = document.getElementById('programData');
                if (existingData) {
                    const jsonText = existingData.textContent || existingData.innerText;
                    programData = JSON.parse(jsonText);
                    populateForm();
                }
            } catch (e) {
                console.error('Error loading existing program data:', e);
            }
            #endif

            // Add default items if creating new program
            #if(!isEdit):
            addAssessment();
            addReview();
            #endif

            // Setup form submission
            document.querySelector('form').addEventListener('submit', function(e) {
                e.preventDefault();
                if (buildAndSubmitForm()) {
                    this.submit();
                }
            });

            // Setup field listeners
            setupFieldListeners();
        });

        function populateForm() {
            document.getElementById('templateId').value = programData.templateId || '';
            document.getElementById('programType').value = programData.programType || '';
            document.getElementById('displayName').value = programData.displayName || '';
            document.getElementById('description').value = programData.description || '';
            document.getElementById('reviewFrequencyDays').value = programData.reviewFrequencyDays || 90;
            document.getElementById('daysToCompleteAssessment').value = programData.daysToCompleteAssessment || 30;

            // Populate assessments
            if (programData.requiredAssessments && programData.requiredAssessments.length > 0) {
                programData.requiredAssessments.forEach(assessment => {
                    addAssessment(assessment);
                });
            }

            // Populate reviews
            if (programData.programReviews && programData.programReviews.length > 0) {
                programData.programReviews.forEach(review => {
                    addReview(review);
                });
            }
        }

        function setupFieldListeners() {
            document.getElementById('templateId').addEventListener('input', updateProgramData);
            document.getElementById('programType').addEventListener('change', updateProgramData);
            document.getElementById('displayName').addEventListener('input', updateProgramData);
            document.getElementById('description').addEventListener('input', updateProgramData);
            document.getElementById('reviewFrequencyDays').addEventListener('input', updateProgramData);
            document.getElementById('daysToCompleteAssessment').addEventListener('input', updateProgramData);
        }

        function updateProgramData() {
            programData.templateId = document.getElementById('templateId').value;
            programData.programType = document.getElementById('programType').value;
            programData.displayName = document.getElementById('displayName').value;
            programData.description = document.getElementById('description').value;
            programData.reviewFrequencyDays = parseInt(document.getElementById('reviewFrequencyDays').value) || 90;
            programData.daysToCompleteAssessment = parseInt(document.getElementById('daysToCompleteAssessment').value) || 30;
        }

        function addAssessment(data = null) {
            const assessmentId = `assessment_${assessmentCounter++}`;
            const assessmentHtml = `
                <div class="dynamic-item" id="${assessmentId}">
                    <div class="dynamic-item-header">
                        <div class="dynamic-item-title">Assessment ${assessmentCounter}</div>
                        <button type="button" class="btn btn-danger btn-small" onclick="removeAssessment('${assessmentId}')">
                            Remove
                        </button>
                    </div>
                    <div class="dynamic-item-fields">
                        <div class="form-group">
                            <label class="form-label">Assessment Template *</label>
                            <div class="form-help" style="padding-bottom: 5px;">Choose from available assessment templates</div>
                            <select
                                class="form-input assessment-template-select full-width-select"
                                onchange="updateAssessmentFields(this, '${assessmentId}')"
                                required
                            >
                                <option value="">Select an assessment template...</option>
                            </select>
                            <div class="checkbox-wrapper-inline">
                                <input
                                    type="checkbox"
                                    id="required_${assessmentId}"
                                    class="assessment-required"
                                    ${data && data.required ? 'checked' : ''}
                                >
                                <label for="required_${assessmentId}" class="checkbox-label">Required</label>
                            </div>
                        </div>
                        #comment: <div class="form-group">
                            <label class="form-label">Item Key *</label>
                            <input
                                type="text"
                                class="form-input assessment-key"
                                placeholder="e.g., adl_iadl"
                                value="${data ? data.itemKey : ''}"
                                readonly
                                required
                            >
                        </div> #endcomment
                        #comment: <div class="form-group">
                            <label class="form-label">Title *</label>
                            <input
                                type="text"
                                class="form-input assessment-title"
                                placeholder="e.g., ADL/IADL Functional Assessment"
                                value="${data ? data.title : ''}"
                                readonly
                                required
                            >
                        </div> #endcomment
                    </div>
                </div>
            `;

            document.getElementById('assessmentsList').insertAdjacentHTML('beforeend', assessmentHtml);

            // Populate the dropdown for the newly added assessment
            const newAssessment = document.getElementById(assessmentId);
            const selectElement = newAssessment.querySelector('.assessment-template-select');
            populateAssessmentDropdown(selectElement);

            // If we have existing data, try to select the matching template
            if (data && data.itemKey) {
                const matchingTemplate = assessmentTemplates.find(template => template.key === data.itemKey);
                if (matchingTemplate) {
                    selectElement.value = JSON.stringify({ key: matchingTemplate.key, name: matchingTemplate.name });
                }
            }
        }

        function addReview(data = null) {
            const reviewId = `review_${reviewCounter++}`;
            const reviewHtml = `
                <div class="dynamic-item" id="${reviewId}">
                    <div class="dynamic-item-header">
                        <div class="dynamic-item-title">Review ${reviewCounter}</div>
                        <button type="button" class="btn btn-danger btn-small" onclick="removeReview('${reviewId}')">
                            Remove
                        </button>
                    </div>
                    <div class="dynamic-item-fields">
                         <div class="form-group">
                            <label class="form-label">Title *</label>
                            <input
                                type="text"
                                class="form-input review-title"
                                placeholder="e.g., Quarterly Program Review"
                                value="${data ? data.title : ''}"
                                oninput="generateReviewKey('${reviewId}')"
                                required
                            >
                            <div class="form-help" style="padding: 5px;">A descriptive name for this review</div>
                            <div class="checkbox-wrapper-inline">
                                <input
                                    type="checkbox"
                                    id="required_${reviewId}"
                                    class="review-required"
                                    ${data && data.required ? 'checked' : ''}
                                >
                                <label for="required_${reviewId}" class="checkbox-label">Required</label>
                            </div>
                        </div>
                        <div class="form-group" style="display: none;">
                            <input
                                type="text"
                                class="form-input review-key"
                                value="${data ? data.itemKey : ''}"
                                readonly
                            >
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('reviewsList').insertAdjacentHTML('beforeend', reviewHtml);
        }

        function removeAssessment(assessmentId) {
            document.getElementById(assessmentId).remove();
        }

        function removeReview(reviewId) {
            document.getElementById(reviewId).remove();
        }

        function buildAndSubmitForm() {
            // Update basic data
            updateProgramData();

            // Collect assessments
            programData.requiredAssessments = [];
            document.querySelectorAll('#assessmentsList .dynamic-item').forEach(item => {
                const itemKey = item.querySelector('.assessment-key').value.trim();
                const title = item.querySelector('.assessment-title').value.trim();
                const required = item.querySelector('.assessment-required').checked;

                if (itemKey && title) {
                    programData.requiredAssessments.push({
                        itemKey: itemKey,
                        title: title,
                        required: required
                    });
                }
            });

            // Collect reviews
            programData.programReviews = [];
            document.querySelectorAll('#reviewsList .dynamic-item').forEach(item => {
                const itemKey = item.querySelector('.review-key').value.trim();
                const title = item.querySelector('.review-title').value.trim();
                const required = item.querySelector('.review-required').checked;

                if (itemKey && title) {
                    programData.programReviews.push({
                        itemKey: itemKey,
                        title: title,
                        required: required
                    });
                }
            });

            // Validate required fields
            if (!programData.templateId || !programData.programType || !programData.displayName || !programData.description) {
                alert('Please fill in all required fields.');
                return false;
            }

            // Set the hidden field with JSON data
            document.getElementById('programConfig').value = JSON.stringify(programData);
            return true;
        }

        // Auto-generate program key from program name
        function generateProgramKey() {
            const programNameInput = document.getElementById('programName');
            const programKeyInput = document.getElementById('programKey');

            if (!programNameInput || !programKeyInput) return;

            const programName = programNameInput.value.trim();
            if (!programName) {
                programKeyInput.value = '';
                return;
            }

            // Generate key: convert to lowercase, replace spaces/hyphens with underscores, remove special chars
            let key = programName
                .toLowerCase()
                .replace(/[-\s]+/g, '_') // Replace hyphens and spaces with underscores
                .replace(/[^a-z0-9_]/g, '') // Remove special characters except underscores
                .replace(/_{2,}/g, '_') // Replace multiple underscores with single
                .replace(/^_|_$/g, ''); // Remove leading/trailing underscores

            // Create abbreviation for common words (check for partial matches)
            const abbreviations = {
                'substance_use_disorder': 'sud',
                'long_term_services_and_supports': 'ltss',
                'behavioral_health': 'behavioral_health',
                'chronic_care_management': 'chronic_care',
                'maternal_and_child_health': 'maternal_child_health',
                'mental_health': 'mental_health',
                'diabetes_management': 'diabetes_mgmt',
                'hypertension_management': 'hypertension_mgmt',
                'care_coordination': 'care_coord'
            };

            // Check if we have a predefined abbreviation (allow partial matches)
            let foundAbbreviation = null;
            const cleanKey = key.replace(/_program$/, '');
            for (const [pattern, abbrev] of Object.entries(abbreviations)) {
                if (cleanKey === pattern || key === pattern) {
                    foundAbbreviation = abbrev;
                    break;
                }
            }

            if (foundAbbreviation) {
                key = foundAbbreviation;
            } else {
                // Create abbreviation from first letters of words (max 10 chars)
                const words = key.split('_');
                if (words.length > 1 && key.length > 15) {
                    key = words.map(word => word.charAt(0)).join('') + '_' + words[words.length - 1];
                    if (key.length > 10) {
                        key = words.map(word => word.charAt(0)).join('');
                    }
                }
            }

            programKeyInput.value = key;
        }

        // Auto-generate review key from review title
        function generateReviewKey(reviewId) {
            const titleInput = document.querySelector(`#${reviewId} .review-title`);
            const keyInput = document.querySelector(`#${reviewId} .review-key`);

            if (!titleInput || !keyInput) return;

            const title = titleInput.value.trim();
            if (!title) {
                keyInput.value = '';
                return;
            }

            // Convert title to key format
            let key = title
                .toLowerCase()
                .replace(/[-\s]+/g, '_') // Replace hyphens and spaces with underscores
                .replace(/[^a-z0-9_]/g, '') // Remove special characters except underscores
                .replace(/_{2,}/g, '_') // Replace multiple underscores with single
                .replace(/^_|_$/g, ''); // Remove leading/trailing underscores

            // Common review abbreviations
            const reviewAbbreviations = {
                'quarterly_program_review': 'quarterly_review',
                'annual_program_review': 'annual_review',
                'monthly_program_review': 'monthly_review',
                'program_effectiveness_review': 'effectiveness_review',
                'quality_assurance_review': 'qa_review',
                'compliance_review': 'compliance_review',
                'outcome_assessment_review': 'outcome_review',
                'care_plan_review': 'care_plan_review',
                'progress_review': 'progress_review',
                'discharge_review': 'discharge_review'
            };

            // Check for predefined abbreviations
            let foundAbbreviation = null;
            for (const [pattern, abbrev] of Object.entries(reviewAbbreviations)) {
                if (key === pattern || key.includes(pattern.replace(/_/g, ''))) {
                    foundAbbreviation = abbrev;
                    break;
                }
            }

            if (foundAbbreviation) {
                key = foundAbbreviation;
            } else {
                // Create abbreviation if too long (max 20 chars for reviews)
                if (key.length > 20) {
                    const words = key.split('_');
                    if (words.length > 1) {
                        // Take first letter of each word except last, plus last word
                        key = words.slice(0, -1).map(word => word.charAt(0)).join('') + '_' + words[words.length - 1];
                        if (key.length > 20) {
                            // If still too long, just use first letters
                            key = words.map(word => word.charAt(0)).join('') + '_review';
                        }
                    }
                }
            }

            keyInput.value = key;
        }

        // Global variable to store assessment templates
        let assessmentTemplates = [];

        // Fetch assessment templates from API
        async function fetchAssessmentTemplates() {
            try {
                const orgId = '#(orgID)'.toLowerCase();
                const response = await fetch(`/admin/templates/assessment?org=${orgId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });

                if (response.ok) {
                    assessmentTemplates = await response.json();
                    console.log('Loaded assessment templates:', assessmentTemplates);

                    // Populate all existing assessment dropdowns
                    document.querySelectorAll('.assessment-template-select').forEach(selectElement => {
                        populateAssessmentDropdown(selectElement);
                    });
                } else {
                    console.error('Failed to fetch assessment templates:', response.status);
                    assessmentTemplates = [];
                }
            } catch (error) {
                console.error('Error fetching assessment templates:', error);
                assessmentTemplates = [];
            }
        }

        // Populate assessment template dropdown
        function populateAssessmentDropdown(selectElement) {
            // Clear existing options except the first one
            selectElement.innerHTML = '<option value="">Select an assessment template...</option>';

            // Add templates as options
            assessmentTemplates.forEach(template => {
                const option = document.createElement('option');
                option.value = JSON.stringify({ key: template.key, name: template.name });
                option.textContent = template.name;
                selectElement.appendChild(option);
            });
        }

        // Update assessment fields when template is selected
        function updateAssessmentFields(selectElement, assessmentId) {
            const selectedValue = selectElement.value;
            if (!selectedValue) return;

            try {
                const templateData = JSON.parse(selectedValue);
                const assessmentItem = document.getElementById(assessmentId);

                // Update the key and title fields
                const keyInput = assessmentItem.querySelector('.assessment-key');
                const titleInput = assessmentItem.querySelector('.assessment-title');

                if (keyInput) keyInput.value = templateData.key;
                if (titleInput) titleInput.value = templateData.name;

            } catch (error) {
                console.error('Error parsing template data:', error);
            }
        }

        // Initialize templates when page loads
        document.addEventListener('DOMContentLoaded', function() {
            fetchAssessmentTemplates();
        });
    </script>
</body>
</html>
