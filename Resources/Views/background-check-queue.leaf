<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Check Review Queue - WellUp Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }
        
        .header {
            background: #fff;
            border-bottom: 1px solid #E5E7EB;            
            padding: 1rem 0;            
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .logo-icon {
            width: 32px;
            height: 32px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
        }
        
        .logo-text {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: #6b7280;
            margin-bottom: 2rem;
        }
        
        .queue-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        
        .queue-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }
        
        .table-header {
            background: #f9fafb;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
        }
        
        .queue-item {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            transition: background-color 0.2s;
        }
        
        .queue-item:hover {
            background: #f9fafb;
        }
        
        .queue-item:last-child {
            border-bottom: none;
        }
        
        .item-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .member-info {
            flex: 1;
        }
        
        .member-name {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }
        
        .member-email {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .risk-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .risk-level-3 {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .risk-level-2 {
            background: #fef3c7;
            color: #d97706;
        }
        
        .risk-level-1 {
            background: #fef7cd;
            color: #ca8a04;
        }

        /* Location Search Styles */
        .location-search-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .location-search-section h2 {
            color: #495057;
            margin-bottom: 5px;
            font-size: 1.4rem;
        }

        .location-search-section p {
            color: #6c757d;
            margin-bottom: 20px;
        }

        .location-search-form .form-row {
            display: grid;
            grid-template-columns: 2fr 3fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .location-search-form .form-group {
            display: flex;
            flex-direction: column;
        }

        .location-search-form label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #495057;
        }

        .location-search-form input,
        .location-search-form select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .location-search-form input:focus,
        .location-search-form select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-help {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 4px;
            font-style: italic;
        }

        .location-search-results {
            margin-top: 20px;
        }

        .search-results-section {
            margin-bottom: 30px;
        }

        .search-results-section h3 {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #dee2e6;
        }

        .exact-matches h3 {
            color: #dc3545;
            border-bottom-color: #dc3545;
        }

        .nearby-offenders h3 {
            color: #ffc107;
            border-bottom-color: #ffc107;
        }

        .location-offender.exact-match {
            border-left: 4px solid #dc3545;
            background: #fef2f2;
        }

        .location-offender.nearby-match {
            border-left: 4px solid #ffc107;
            background: #fffbf0;
        }

        .location-search-summary {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .location-search-summary h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .location-search-summary p {
            margin-bottom: 5px;
            color: #424242;
        }

        .offender-address,
        .offender-location {
            font-size: 0.9rem;
            color: #666;
            margin-top: 2px;
        }

        @media (max-width: 768px) {
            .location-search-form .form-row {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }
        
        .item-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
        }
        
        .detail-label {
            font-size: 0.75rem;
            color: #6b7280;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .detail-value {
            color: #1f2937;
            font-weight: 500;
        }
        
        .item-actions {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-approve {
            background: #10b981;
            color: white;
        }
        
        .btn-approve:hover {
            background: #059669;
        }
        
        .btn-reject {
            background: #ef4444;
            color: white;
        }
        
        .btn-reject:hover {
            background: #dc2626;
        }
        
        .btn-view {
            background: #6366f1;
            color: white;
        }
        
        .btn-view:hover {
            background: #4f46e5;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }
        
        .empty-state h3 {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: #374151;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #6366f1;
            text-decoration: none;
            margin-bottom: 1rem;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: #4f46e5;
        }

        /* Expandable Details Styles */
        .item-details-expanded {
            border-top: 1px solid #e5e7eb;
            padding: 20px;
            background: #f9fafb;
            margin-top: 15px;
            border-radius: 0 0 8px 8px;
        }

        .loading-spinner {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 40px;
            color: #6b7280;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .detailed-content {
            max-height: 600px;
            overflow-y: auto;
        }

        .offender-record {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .offender-record.selected {
            border-color: #007bff;
            background: #f8f9ff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
        }

        .offender-record.not-member {
            border-color: #dc3545;
            background: #fff5f5;
            opacity: 0.7;
        }

        .offender-header {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: flex-start;
        }

        .offender-selection {
            flex-shrink: 0;
            margin-right: 15px;
        }

        .selection-controls {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 140px;
        }

        .selection-option {
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            padding: 6px 10px;
            border-radius: 4px;
            transition: background-color 0.2s;
            font-size: 13px;
        }

        .selection-option:hover {
            background-color: #f3f4f6;
        }

        .selection-option input[type="radio"] {
            margin: 0;
        }

        .selection-option.not-member .selection-label {
            color: #dc3545;
        }

        .selection-label {
            font-weight: 500;
            color: #374151;
        }

        .offender-photo {
            flex-shrink: 0;
        }

        .offender-photo img {
            width: 80px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #d1d5db;
        }

        .offender-photo .no-photo {
            width: 80px;
            height: 100px;
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-size: 12px;
            text-align: center;
        }

        .offender-basic {
            flex: 1;
        }

        .offender-name {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #111827;
        }

        .offender-aliases {
            color: #6b7280;
            font-style: italic;
            margin-top: 5px;
        }

        .offender-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .offender-details-grid {
                grid-template-columns: 1fr;
            }
        }

        .detail-section {
            background: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .detail-section h5 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .detail-section.full-width {
            grid-column: 1 / -1;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 4px 0;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        .detail-row .label {
            font-weight: 500;
            color: #6b7280;
            min-width: 80px;
        }

        .detail-row .value {
            color: #111827;
            text-align: right;
            flex: 1;
            margin-left: 10px;
        }

        .court-record {
            background: #fef3c7;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #f59e0b;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-word;
        }

        .match-summary {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .match-summary h4 {
            margin: 0 0 15px 0;
            color: #111827;
            font-size: 16px;
            font-weight: 600;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .summary-label {
            font-weight: 500;
            color: #6b7280;
        }

        .summary-value {
            color: #111827;
            font-weight: 600;
        }

        .no-matches {
            text-align: center;
            padding: 40px;
            color: #6b7280;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 15px;
            color: #dc2626;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon"></div>
                <div class="logo-text">wellup</div>
            </div>
            
            <div class="user-menu">
                <span class="user-info">Welcome, #(adminUsername)</span>
                <form method="POST" action="/admin/logout" style="display: inline;">
                    <button type="submit" class="logout-btn">Logout</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="container">
        <a href="/admin/dashboard" class="back-link">
            ← Back to Dashboard
        </a>
        
        <h1 class="page-title">Background Check Review Queue</h1>
        <p class="page-subtitle">Review and approve background check results that require manual verification</p>

        <!-- Location Search Section -->
        <div class="location-search-section">
            <h2>Location-Based Offender Search</h2>
            <p>Search for registered offenders within a 2-mile radius of a specific address</p>

            <form id="location-search-form" class="location-search-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="member-select">Member:</label>
                        <select id="member-select" name="memberId" required>
                            <option value="">Select a member...</option>
                            <!-- Members will be populated dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="search-address">Address:</label>
                        <input type="text" id="search-address" name="address" placeholder="Try: 1700 Belmont Ave, 2100 Pine St, or 1500 Broadway" required>                        
                    </div>
                    <div class="form-group">
                        <label for="latitude">Latitude:</label>
                        <input type="number" id="latitude" name="latitude" step="any" placeholder="47.616887" required>
                    </div>
                    <div class="form-group">
                        <label for="longitude">Longitude:</label>
                        <input type="number" id="longitude" name="longitude" step="any" placeholder="-122.324593" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Search Location</button>
                    </div>
                </div>
            </form>

            <div id="location-search-results" class="location-search-results" style="display: none;">
                <!-- Results will be populated here -->
            </div>
        </div>

        <div class="queue-stats">
            <div class="stat-card">
                <div class="stat-number">#(pendingCount)</div>
                <div class="stat-label">Pending Reviews</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">#(totalMatches)</div>
                <div class="stat-label">Total Matches Found</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">#(highRiskCount)</div>
                <div class="stat-label">High Risk (Level III)</div>
            </div>
        </div>

        <div class="queue-table">
            <div class="table-header">
                Background Check Queue
            </div>
            
            #if(count(queueItems) > 0):
                #for(item in queueItems):
                <div class="queue-item">
                    <div class="item-header">
                        <div class="member-info">
                            <div class="member-name">#(item.member.firstName) #(item.member.lastName)</div>
                            <div class="member-email">#(item.member.email)</div>
                        </div>
                        #if(item.highestRiskLevel):
                            <div class="risk-badge risk-level-#(item.riskLevelClass)">#(item.highestRiskLevel)</div>
                        #endif
                    </div>
                    
                    <div class="item-details">
                        <div class="detail-item">
                            <div class="detail-label">Search Name</div>
                            <div class="detail-value">#(item.fullName)</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Zip Code</div>
                            <div class="detail-value">#(item.zipCode)</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Matches Found</div>
                            <div class="detail-value">#(item.matchedCount)</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Triggered By</div>
                            <div class="detail-value">#(item.triggeredBy.firstName) #(item.triggeredBy.lastName)</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Date</div>
                            <div class="detail-value">#(item.createdAtFormatted)</div>
                        </div>
                    </div>
                    
                    <!-- NEW: Expandable details section -->
                    <div class="item-details-expanded" id="details-#(item.id)" style="display: none;">
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                            <span>Loading detailed information...</span>
                        </div>
                        <div class="detailed-content" style="display: none;">
                            <!-- Comprehensive offender information will be loaded here -->
                        </div>
                    </div>

                    <div class="item-actions">
                        <button onclick="toggleDetails('#(item.id)')" class="btn btn-view">
                            <span class="toggle-text">View Details</span>
                        </button>
                        <button onclick="approveCheck('#(item.id)')" class="btn btn-approve">
                            Approve
                        </button>
                        <button onclick="rejectCheck('#(item.id)')" class="btn btn-reject">
                            Reject
                        </button>
                    </div>
                </div>
                #endfor
            #else:
                <div class="empty-state">
                    <h3>No Pending Reviews</h3>
                    <p>All background checks have been reviewed.</p>
                </div>
            #endif
        </div>
    </div>

    <script>
        // Toggle expandable details section
        function toggleDetails(queueId) {
            const detailsSection = document.getElementById(`details-${queueId}`);
            const toggleButton = event.target.closest('button');
            const toggleText = toggleButton.querySelector('.toggle-text');

            if (detailsSection.style.display === 'none' || detailsSection.style.display === '') {
                // Expand details
                detailsSection.style.display = 'block';
                toggleText.textContent = 'Hide Details';
                loadDetailedInformation(queueId);
            } else {
                // Collapse details
                detailsSection.style.display = 'none';
                toggleText.textContent = 'View Details';
            }
        }

        // Load detailed information from API
        function loadDetailedInformation(queueId) {
            const detailsSection = document.getElementById(`details-${queueId}`);
            const loadingSpinner = detailsSection.querySelector('.loading-spinner');
            const detailedContent = detailsSection.querySelector('.detailed-content');

            // Show loading state
            loadingSpinner.style.display = 'flex';
            detailedContent.style.display = 'none';
            console.log('Bearer ' + getAuthToken());

            // Fetch detailed data from API
            fetch(`/api/background-checks/queue/${queueId}`, {
                headers: {
                    'Authorization': 'Bearer ' + getAuthToken()
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                // Hide loading, show content
                loadingSpinner.style.display = 'none';
                detailedContent.style.display = 'block';

                // Populate detailed information
                renderDetailedInformation(detailedContent, data);
            })
            .catch(error => {
                loadingSpinner.style.display = 'none';
                detailedContent.style.display = 'block';
                detailedContent.innerHTML = `
                    <div class="error-message">
                        <strong>Error loading details:</strong> ${error.message}
                    </div>
                `;
                console.error('Error loading queue item details:', error);
            });
        }

        // Render detailed information in the expanded section
        function renderDetailedInformation(container, data) {
            const { rawResponse, member, triggeredBy, fullName, zipCode, matchedCount, highestRiskLevel, createdAt } = data;

            let html = `
                <div class="match-summary">
                    <h4>Background Check Summary</h4>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">Member:</span>
                            <span class="summary-value">${member.firstName} ${member.lastName}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Search Name:</span>
                            <span class="summary-value">${fullName}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Zip Code:</span>
                            <span class="summary-value">${zipCode}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Matches Found:</span>
                            <span class="summary-value">${matchedCount}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Highest Risk:</span>
                            <span class="summary-value">${highestRiskLevel || 'None'}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Triggered By:</span>
                            <span class="summary-value">${triggeredBy.firstName} ${triggeredBy.lastName}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Date:</span>
                            <span class="summary-value">${new Date(createdAt).toLocaleString()}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Search Query:</span>
                            <span class="summary-value">${rawResponse.searchQuery.name} (${rawResponse.searchQuery.zipCode})</span>
                        </div>
                    </div>
                </div>
            `;

            if (rawResponse.results && rawResponse.results.length > 0) {
                rawResponse.results.forEach((offender, index) => {
                    html += renderOffenderRecord(offender, index + 1);
                });
            } else {
                html += `
                    <div class="no-matches">
                        <h3>No Matches Found</h3>
                        <p>The background check completed successfully with no offender registry matches.</p>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        // Render individual offender record
        function renderOffenderRecord(offender, index) {
            const riskClass = getRiskLevelClass(offender.riskLevel);

            return `
                <div class="offender-record" data-offender-index="${index - 1}">
                    <div class="offender-header">
                        <div class="offender-selection">
                            <div class="selection-controls">
                                <label class="selection-option">
                                    <input type="radio" name="selected-offender" value="${index - 1}" onchange="handleOffenderSelection(${index - 1})">
                                    <span class="selection-label">This is our member</span>
                                </label>
                                <label class="selection-option not-member">
                                    <input type="radio" name="not-member-${index - 1}" value="not-member" onchange="handleOffenderSelection(${index - 1}, false)">
                                    <span class="selection-label">Not our member</span>
                                </label>
                            </div>
                        </div>
                        <div class="offender-photo">
                            ${offender.photo_url ?
                                `<img src="${offender.photo_url}" alt="">` :
                                '<div class="no-photo">No Photo Available</div>'
                            }
                        </div>
                        <div class="offender-basic">
                            <h4 class="offender-name">Match #${index}: ${offender.name}</h4>
                            ${offender.riskLevel ? `<div class="risk-badge risk-level-${riskClass}">${offender.riskLevel}</div>` : ''}
                            ${offender.aliases ? `<div class="offender-aliases">Aliases: ${offender.aliases}</div>` : ''}
                        </div>
                    </div>

                    <div class="offender-details-grid">
                        <div class="detail-section">
                            <h5>Personal Information</h5>
                            ${offender.age ? `<div class="detail-row"><span class="label">Age:</span><span class="value">${offender.age}</span></div>` : ''}
                            ${offender.gender ? `<div class="detail-row"><span class="label">Gender:</span><span class="value">${offender.gender}</span></div>` : ''}
                            ${offender.race ? `<div class="detail-row"><span class="label">Race:</span><span class="value">${offender.race}</span></div>` : ''}
                            ${offender.updateDatetime ? `<div class="detail-row"><span class="label">Last Updated:</span><span class="value">${new Date(offender.updateDatetime).toLocaleDateString()}</span></div>` : ''}
                        </div>

                        <div class="detail-section">
                            <h5>Physical Description</h5>
                            ${offender.height ? `<div class="detail-row"><span class="label">Height:</span><span class="value">${offender.height}</span></div>` : ''}
                            ${offender.weight ? `<div class="detail-row"><span class="label">Weight:</span><span class="value">${offender.weight}</span></div>` : ''}
                            ${offender.eyeColor ? `<div class="detail-row"><span class="label">Eyes:</span><span class="value">${offender.eyeColor}</span></div>` : ''}
                            ${offender.hairColor ? `<div class="detail-row"><span class="label">Hair:</span><span class="value">${offender.hairColor}</span></div>` : ''}
                            ${offender.marksScarsTattoos ? `<div class="detail-row"><span class="label">Marks/Scars:</span><span class="value">${offender.marksScarsTattoos}</span></div>` : ''}
                        </div>

                        <div class="detail-section">
                            <h5>Location</h5>
                            ${offender.address ? `<div class="detail-row"><span class="label">Address:</span><span class="value">${offender.address}</span></div>` : ''}
                            ${offender.city || offender.state ? `<div class="detail-row"><span class="label">City, State:</span><span class="value">${offender.city || ''}, ${offender.state || ''} ${offender.zipCode || ''}</span></div>` : ''}
                            ${offender.location ? `<div class="detail-row"><span class="label">Coordinates:</span><span class="value">${offender.location}</span></div>` : ''}
                        </div>

                        ${offender.court_record ? `
                            <div class="detail-section full-width">
                                <h5>Criminal History</h5>
                                <div class="court-record">${formatCourtRecord(offender.court_record)}</div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // Format court record for better readability
        function formatCourtRecord(courtRecord) {
            if (!courtRecord || typeof courtRecord !== 'string') {
                return 'Invalid court record data';
            }

            return courtRecord
                .replace(/\|/g, '<br><br>')
                .replace(/Crime:/g, '🔸 Crime:')
                .replace(/Conviction date:/g, '📅 Conviction date:')
                .replace(/Statute:/g, '⚖️ Statute:')
                .replace(/Jurisdiction:/g, '🏛️ Jurisdiction:');
        }

        // Get risk level class for styling
        function getRiskLevelClass(riskLevel) {
            if (!riskLevel) return '';
            if (riskLevel.includes('III')) return '3';
            if (riskLevel.includes('II')) return '2';
            if (riskLevel.includes('I')) return '1';
            return '';
        }

        // Global variable to track selected offender
        let selectedOffenderData = {
            queueId: null,
            selectedIndex: null,
            matchSelections: []
        };

        function handleOffenderSelection(offenderIndex, isCorrectPerson = true) {
            const queueId = event.target.closest('.queue-item').querySelector('[onclick*="toggleDetails"]').getAttribute('onclick').match(/toggleDetails\('([^']+)'/)[1];

            // Update global selection data
            selectedOffenderData.queueId = queueId;

            if (isCorrectPerson) {
                selectedOffenderData.selectedIndex = offenderIndex;
            }

            // Update match selections array
            const existingSelection = selectedOffenderData.matchSelections.find(s => s.offenderIndex === offenderIndex);
            if (existingSelection) {
                existingSelection.isSelected = true;
                existingSelection.isCorrectPerson = isCorrectPerson;
            } else {
                selectedOffenderData.matchSelections.push({
                    offenderIndex: offenderIndex,
                    isSelected: true,
                    isCorrectPerson: isCorrectPerson,
                    adminNotes: null
                });
            }

            // Update UI
            updateOffenderRecordAppearance(offenderIndex, isCorrectPerson);

            // Enable/disable approve button based on selection
            updateApproveButtonState(queueId);

            console.log('Selection updated:', selectedOffenderData);
        }

        function updateOffenderRecordAppearance(offenderIndex, isCorrectPerson) {
            const offenderRecord = document.querySelector(`[data-offender-index="${offenderIndex}"]`);
            if (offenderRecord) {
                // Remove existing classes
                offenderRecord.classList.remove('selected', 'not-member');

                // Add appropriate class
                if (isCorrectPerson) {
                    offenderRecord.classList.add('selected');
                } else {
                    offenderRecord.classList.add('not-member');
                }
            }
        }

        function updateApproveButtonState(queueId) {
            const approveButton = document.querySelector(`[onclick*="approveCheck('${queueId}')"]`);
            const hasSelection = selectedOffenderData.selectedIndex !== null;

            if (approveButton) {
                if (hasSelection) {
                    approveButton.disabled = false;
                    approveButton.style.opacity = '1';
                    approveButton.title = 'Approve selected match';
                } else {
                    approveButton.disabled = true;
                    approveButton.style.opacity = '0.5';
                    approveButton.title = 'Please select which person matches the member';
                }
            }
        }

        function approveCheck(queueId) {
            // Check if an offender has been selected
            if (!selectedOffenderData.selectedIndex && selectedOffenderData.selectedIndex !== 0) {
                alert('Please select which person matches the member before approving.');
                return;
            }

            const notes = prompt("Add any notes for this approval (optional):");
            if (notes !== null) {
                const requestData = {
                    notes: notes,
                    selectedOffenderIndex: selectedOffenderData.selectedIndex,
                    matchSelections: selectedOffenderData.matchSelections
                };

                fetch(`/api/background-checks/queue/${queueId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + getAuthToken()
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Background check approved successfully');
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error approving background check');
                    console.error(error);
                });
            }
        }
        
        function rejectCheck(queueId) {
            const notes = prompt("Add notes explaining why this is being rejected:");
            if (notes !== null && notes.trim() !== '') {
                fetch(`/api/background-checks/queue/${queueId}/reject`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + getAuthToken()
                    },
                    body: JSON.stringify({ notes: notes })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Background check rejected successfully');
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error rejecting background check');
                    console.error(error);
                });
            } else {
                alert('Notes are required when rejecting a background check');
            }
        }
        
        function getAuthToken() {
            // Get the admin token from the template context
            return '#(adminToken)';
        }

        // Location Search Functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Load members for the dropdown
            loadMembers();

            // Handle location search form submission
            document.getElementById('location-search-form').addEventListener('submit', handleLocationSearch);

            // Auto-populate coordinates for test addresses
            document.getElementById('search-address').addEventListener('input', handleAddressInput);
        });

        function loadMembers() {
            // For now, add some mock members - in production this would fetch from API
            const memberSelect = document.getElementById('member-select');
            const mockMembers = [
                { id: '123e4567-e89b-12d3-a456-426614174000', name: 'John Smith' },
                { id: '123e4567-e89b-12d3-a456-426614174001', name: 'Jane Doe' },
                { id: '123e4567-e89b-12d3-a456-426614174002', name: 'Michael Johnson' }
            ];

            mockMembers.forEach(member => {
                const option = document.createElement('option');
                option.value = member.id;
                option.textContent = member.name;
                memberSelect.appendChild(option);
            });
        }

        function handleAddressInput(event) {
            const address = event.target.value.toLowerCase();
            const latInput = document.getElementById('latitude');
            const lngInput = document.getElementById('longitude');

            // Auto-populate coordinates for test addresses
            if (address.includes('1700') && address.includes('belmont')) {
                latInput.value = '47.616887';
                lngInput.value = '-122.324593';
            } else if (address.includes('2100') && address.includes('pine')) {
                latInput.value = '47.614523';
                lngInput.value = '-122.331847';
            } else if (address.includes('1500') && address.includes('broadway')) {
                latInput.value = '47.615234';
                lngInput.value = '-122.320567';
            }
        }

        function handleLocationSearch(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const searchData = {
                memberId: formData.get('memberId'),
                address: formData.get('address'),
                latitude: parseFloat(formData.get('latitude')),
                longitude: parseFloat(formData.get('longitude'))
            };

            // Validate form data
            if (!searchData.memberId || !searchData.address || !searchData.latitude || !searchData.longitude) {
                alert('Please fill in all fields');
                return;
            }

            // Show loading state
            const resultsContainer = document.getElementById('location-search-results');
            resultsContainer.style.display = 'block';
            resultsContainer.innerHTML = '<div class="loading-spinner">Searching for offenders...</div>';

            // Perform location search
            fetch('/api/background-checks/location-search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + getAuthToken()
                },
                body: JSON.stringify(searchData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                displayLocationSearchResults(data);
            })
            .catch(error => {
                resultsContainer.innerHTML = `
                    <div class="error-message">
                        <strong>Error performing location search:</strong> ${error.message}
                    </div>
                `;
                console.error('Location search error:', error);
            });
        }

        function displayLocationSearchResults(data) {
            const resultsContainer = document.getElementById('location-search-results');

            let html = `
                <div class="location-search-summary">
                    <h3>Location Search Results</h3>
                    <p>Found ${data.totalCount} registered offenders within 2 miles of "${data.searchQuery.address}"</p>
                    <p><strong>Queue ID:</strong> ${data.queueId} (added to review queue)</p>
                </div>
            `;

            // Exact Address Matches Section
            if (data.exactMatches && data.exactMatches.length > 0) {
                html += `
                    <div class="search-results-section exact-matches">
                        <h3>🎯 Exact Address Matches (${data.exactMatches.length})</h3>
                        <p>Offenders whose registered address matches the searched location</p>
                `;

                data.exactMatches.forEach((offender, index) => {
                    html += renderLocationOffenderRecord(offender, index, 'exact');
                });

                html += `</div>`;
            }

            // Nearby Offenders Section
            if (data.nearbyOffenders && data.nearbyOffenders.length > 0) {
                html += `
                    <div class="search-results-section nearby-offenders">
                        <h3>📍 Nearby Offenders (${data.nearbyOffenders.length})</h3>
                        <p>Other registered offenders within 2-mile radius</p>
                `;

                data.nearbyOffenders.forEach((offender, index) => {
                    html += renderLocationOffenderRecord(offender, index + data.exactMatches.length, 'nearby');
                });

                html += `</div>`;
            }

            if (data.totalCount === 0) {
                html += `
                    <div class="no-matches">
                        <h3>✅ No Matches Found</h3>
                        <p>No registered offenders found within 2 miles of the specified address.</p>
                    </div>
                `;
            }

            resultsContainer.innerHTML = html;
        }

        function renderLocationOffenderRecord(offender, index, type) {
            const riskClass = getRiskLevelClass(offender.riskLevel);
            const typeClass = type === 'exact' ? 'exact-match' : 'nearby-match';

            return `
                <div class="offender-record location-offender ${typeClass}" data-offender-index="${index}">
                    <div class="offender-header">
                        <div class="offender-photo">
                            ${offender.photoUrl ?
                                `<img src="${offender.photoUrl}" alt="Offender Photo" onerror="this.parentElement.innerHTML='<div class=\\"no-photo\\">Photo Failed to Load</div>'">` :
                                '<div class="no-photo">No Photo Available</div>'
                            }
                        </div>
                        <div class="offender-basic">
                            <h4 class="offender-name">${type === 'exact' ? '🎯' : '📍'} ${offender.name}</h4>
                            ${offender.riskLevel ? `<div class="risk-badge risk-level-${riskClass}">${offender.riskLevel}</div>` : ''}
                            ${offender.aliases ? `<div class="offender-aliases">Aliases: ${offender.aliases}</div>` : ''}
                            <div class="offender-address">${offender.address || 'Address not available'}</div>
                            <div class="offender-location">${offender.city || ''}, ${offender.state || ''} ${offender.zipCode || ''}</div>
                        </div>
                    </div>

                    <div class="offender-details-grid">
                        <div class="detail-section">
                            <h5>Personal Information</h5>
                            ${offender.age ? `<div class="detail-row"><span class="label">Age:</span><span class="value">${offender.age}</span></div>` : ''}
                            ${offender.gender ? `<div class="detail-row"><span class="label">Gender:</span><span class="value">${offender.gender}</span></div>` : ''}
                            ${offender.race ? `<div class="detail-row"><span class="label">Race:</span><span class="value">${offender.race}</span></div>` : ''}
                            ${offender.updateDatetime ? `<div class="detail-row"><span class="label">Last Updated:</span><span class="value">${new Date(offender.updateDatetime).toLocaleDateString()}</span></div>` : ''}
                        </div>

                        <div class="detail-section">
                            <h5>Physical Description</h5>
                            ${offender.height ? `<div class="detail-row"><span class="label">Height:</span><span class="value">${offender.height}</span></div>` : ''}
                            ${offender.weight ? `<div class="detail-row"><span class="label">Weight:</span><span class="value">${offender.weight}</span></div>` : ''}
                            ${offender.eyeColor ? `<div class="detail-row"><span class="label">Eye Color:</span><span class="value">${offender.eyeColor}</span></div>` : ''}
                            ${offender.hairColor ? `<div class="detail-row"><span class="label">Hair Color:</span><span class="value">${offender.hairColor}</span></div>` : ''}
                            ${offender.marksScarsTattoos ? `<div class="detail-row"><span class="label">Marks/Scars/Tattoos:</span><span class="value">${offender.marksScarsTattoos}</span></div>` : ''}
                        </div>

                        <div class="detail-section">
                            <h5>Location Information</h5>
                            ${offender.address ? `<div class="detail-row"><span class="label">Address:</span><span class="value">${offender.address}</span></div>` : ''}
                            ${offender.city || offender.state ? `<div class="detail-row"><span class="label">City, State:</span><span class="value">${offender.city || ''}, ${offender.state || ''} ${offender.zipCode || ''}</span></div>` : ''}
                            ${offender.location ? `<div class="detail-row"><span class="label">Coordinates:</span><span class="value">${offender.location}</span></div>` : ''}
                        </div>

                        ${offender.courtRecord ? `
                            <div class="detail-section full-width">
                                <h5>Criminal History</h5>
                                <div class="court-record">${formatCourtRecord(offender.courtRecord)}</div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
