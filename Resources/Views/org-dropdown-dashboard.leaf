<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organization Dropdown Manager - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: white;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(to bottom, #FD8205, #E97100);
            color: white;
            padding: 20px 30px;
            text-align: center;
            flex-shrink: 0;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #F9FAFB;
            border-right: 1px solid #E5E7EB;
            overflow-y: auto;
            flex-shrink: 0;
        }

        .content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }
        
        .section-list {
            padding: 0;
        }

        .section-item {
            padding: 16px 20px;
            border-bottom: 1px solid #E5E7EB;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-item:hover {
            background: #F3F4F6;
        }

        .section-item.active {
            background: #EBF8FF;
            border-right: 3px solid #FD8205;
        }

        .section-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .section-count {
            font-size: 12px;
            color: #6B7280;
            background: #E5E7EB;
            padding: 2px 8px;
            border-radius: 12px;
        }

        .detail-view {
            display: none;
        }

        .detail-view.active {
            display: block;
        }

        .detail-header {
            background: #F9FAFB;
            padding: 20px;
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: -30px -30px 30px -30px;
        }

        .detail-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
        }

        .detail-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: linear-gradient(to bottom, #FD8205, #E97100);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(253, 130, 5, 0.3);
        }
        
        .btn-danger {
            background: #EF4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #DC2626;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px 20px;
            text-align: left;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .items-table th {
            background: #F9FAFB;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }
        
        .items-table td {
            color: #6B7280;
            font-size: 14px;
        }
        
        .items-table tr:hover {
            background: #F9FAFB;
        }
        
        .color-badge {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 1px solid #E5E7EB;
        }
        
        .empty-state {
            padding: 40px;
            text-align: center;
            color: #6B7280;
        }

        .empty-state p {
            font-size: 16px;
            margin-bottom: 20px;
        }

        .defaults-preview {
            background: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .defaults-preview h4 {
            color: #374151;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .defaults-preview p {
            color: #6B7280;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .defaults-list {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 16px;
        }

        .default-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 6px;
            margin-bottom: 4px;
        }

        .default-title {
            font-size: 14px;
            color: #374151;
            flex: 1;
        }

        .default-key {
            font-size: 12px;
            color: #6B7280;
            background: #F3F4F6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }

        .empty-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 16px;
        }

        .import-items-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #E5E7EB;
            border-radius: 6px;
            padding: 12px;
            background: #F9FAFB;
        }

        .import-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 4px;
            margin-bottom: 4px;
        }

        .import-item:last-child {
            margin-bottom: 0;
        }

        .import-item input[type="checkbox"] {
            margin: 0;
        }

        .import-item-content {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .import-item-title {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }

        .import-item-key {
            font-size: 12px;
            color: #6B7280;
            background: #F3F4F6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .actions-cell {
            display: flex;
            gap: 8px;
        }
        
        .date-text {
            font-size: 12px;
            color: #9CA3AF;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #9CA3AF;
        }
        
        .close:hover {
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Organization Dropdown Manager</h1>
            <p>Manage dropdown values for your organization</p>
        </div>

        <div class="main-content">
            <!-- Sidebar with section list -->
            <div class="sidebar">
                <div class="section-list">
                    #for(section in sections):
                    <div class="section-item" onclick="showSection('#(section.key)')" id="section-#(section.key)">
                        <span class="section-name">#(section.displayName)</span>
                        <span class="section-count">#(count(section.items))</span>
                    </div>
                    #endfor
                </div>
            </div>

            <!-- Detail view -->
            <div class="content">
                #for(section in sections):
                <div class="detail-view" id="detail-#(section.key)">
                    <div class="detail-header">
                        <h2 class="detail-title">#(section.displayName)</h2>
                        <div class="detail-actions">
                            <a href="/org/#(orgID)/constants/#(section.key)/create" class="btn btn-primary">
                                ➕ Create New Item
                            </a>
                            #if(section.isEmpty && section.hasDefaults):
                            <button onclick="showImportModal('#(section.key)', '#(section.displayName)')" class="btn btn-secondary">
                                📥 Import Defaults
                            </button>
                            #endif
                            #if(count(section.items) > 0):
                            <button onclick="confirmRemoveAll('#(section.key)', '#(section.displayName)')" class="btn btn-danger btn-small">
                                🗑️ Remove All
                            </button>
                            #endif
                        </div>
                    </div>

                    #if(count(section.items) > 0):
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Key</th>
                                #if(section.supportsColor):
                                <th>Color</th>
                                #endif
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            #for(item in section.items):
                            <tr>
                                <td><strong>#(item.title)</strong></td>
                                <td><code>#(item.key)</code></td>
                                #if(section.supportsColor):
                                <td>
                                    #if(item.color):
                                    <span class="color-badge" style="background-color: #(item.color);"></span>
                                    #else:
                                    <span class="color-badge" style="background-color: #E5E7EB;"></span>
                                    #endif
                                </td>
                                #endif
                                <td class="date-text">
                                    Recently updated
                                </td>
                                <td>
                                    <div class="actions-cell">
                                        <button onclick="confirmDelete('#(item.key)', '#(item.title)', '#(section.key)')" class="btn btn-danger btn-small">
                                            Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            #endfor
                        </tbody>
                    </table>
                    #else:
                    <div class="empty-state">
                        <p>No items in this section yet.</p>
                        #if(section.hasDefaults):
                        <div class="defaults-preview">
                            <h4>Platform Defaults Available</h4>
                            <p>The following default items are available for import:</p>
                            <div class="defaults-list">
                                #for(defaultItem in section.defaultItems):
                                <div class="default-item">
                                    #if(section.supportsColor && defaultItem.color):
                                    <span class="color-badge" style="background-color: #(defaultItem.color);"></span>
                                    #endif
                                    <span class="default-title">#(defaultItem.title)</span>
                                    <code class="default-key">#(defaultItem.key)</code>
                                </div>
                                #endfor
                            </div>
                            <div class="empty-actions">
                                <button onclick="showImportModal('#(section.key)', '#(section.displayName)')" class="btn btn-secondary">
                                    📥 Import Defaults
                                </button>
                                <a href="/org/#(orgID)/constants/#(section.key)/create" class="btn btn-primary">
                                    Create Custom Item
                                </a>
                            </div>
                        </div>
                        #else:
                        <a href="/org/#(orgID)/constants/#(section.key)/create" class="btn btn-primary">
                            Create First Item
                        </a>
                        #endif
                    </div>
                    #endif
                </div>
                #endfor
            </div>
        </div>
    </div>
    
    <!-- Confirmation Modals -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <span class="close" onclick="closeModal('deleteModal')">&times;</span>
            </div>
            <p>Are you sure you want to delete "<span id="deleteItemName"></span>"?</p>
            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                <button onclick="closeModal('deleteModal')" class="btn" style="background: #E5E7EB; color: #374151;">Cancel</button>
                <button id="confirmDeleteBtn" class="btn btn-danger" onclick="performDelete()">Delete</button>
            </div>
        </div>
    </div>
    
    <div id="removeAllModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Remove All</h3>
                <span class="close" onclick="closeModal('removeAllModal')">&times;</span>
            </div>
            <p>Are you sure you want to remove all items from "<span id="removeAllSectionName"></span>"?</p>
            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                <button onclick="closeModal('removeAllModal')" class="btn" style="background: #E5E7EB; color: #374151;">Cancel</button>
                <form id="removeAllForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Remove All</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Import Defaults Modal -->
    <div id="importModal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">Import Default Items</h3>
                <span class="close" onclick="closeModal('importModal')">&times;</span>
            </div>
            <p>Select which default items to import for "<span id="importSectionName"></span>":</p>
            <div id="importItemsList" class="import-items-list">
                <!-- Items will be populated by JavaScript -->
            </div>
            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: space-between;">
                <div>
                    <button onclick="selectAllDefaults()" class="btn" style="background: #F3F4F6; color: #374151;">Select All</button>
                    <button onclick="deselectAllDefaults()" class="btn" style="background: #F3F4F6; color: #374151;">Deselect All</button>
                </div>
                <div>
                    <button onclick="closeModal('importModal')" class="btn" style="background: #E5E7EB; color: #374151;">Cancel</button>
                    <button id="importConfirmBtn" onclick="performImport()" class="btn btn-primary">Import Selected</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentDeleteUrl = '';
        let currentSection = '';
        let currentImportSection = '';
        let defaultConstants = null;

        // Initialize the first section as active and fetch defaults
        document.addEventListener('DOMContentLoaded', function() {
            const firstSection = document.querySelector('.section-item');
            if (firstSection) {
                const sectionKey = firstSection.id.replace('section-', '');
                showSection(sectionKey);
            }

            // Fetch default constants
            fetchDefaultConstants();
        });

        async function fetchDefaultConstants() {
            try {
                const response = await fetch('/org/#(orgID)/constants/defaults');
                if (response.ok) {
                    defaultConstants = await response.json();
                }
            } catch (error) {
                console.error('Failed to fetch default constants:', error);
            }
        }

        function showSection(sectionKey) {
            // Hide all detail views
            document.querySelectorAll('.detail-view').forEach(view => {
                view.classList.remove('active');
            });

            // Remove active class from all section items
            document.querySelectorAll('.section-item').forEach(item => {
                item.classList.remove('active');
            });

            // Show selected detail view
            const detailView = document.getElementById('detail-' + sectionKey);
            if (detailView) {
                detailView.classList.add('active');
            }

            // Add active class to selected section item
            const sectionItem = document.getElementById('section-' + sectionKey);
            if (sectionItem) {
                sectionItem.classList.add('active');
            }

            currentSection = sectionKey;
        }

        function confirmDelete(itemId, itemName, sectionKey) {
            document.getElementById('deleteItemName').textContent = itemName;
            currentDeleteUrl = '/org/#(orgID)/constants/' + sectionKey + '/' + itemId;
            document.getElementById('deleteModal').style.display = 'block';
        }

        async function performDelete() {
            const deleteBtn = document.getElementById('confirmDeleteBtn');

            // Show loading state
            deleteBtn.disabled = true;
            deleteBtn.textContent = 'Deleting...';

            try {
                const response = await fetch(currentDeleteUrl, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    // Success - refresh the page
                    window.location.reload();
                } else {
                    // Handle error
                    alert('Failed to delete item. Please try again.');
                    deleteBtn.disabled = false;
                    deleteBtn.textContent = 'Delete';
                }
            } catch (error) {
                console.error('Delete error:', error);
                alert('An error occurred. Please try again.');
                deleteBtn.disabled = false;
                deleteBtn.textContent = 'Delete';
            }
        }

        function confirmRemoveAll(sectionKey, sectionName) {
            document.getElementById('removeAllSectionName').textContent = sectionName;
            document.getElementById('removeAllForm').action = '/org/#(orgID)/constants/' + sectionKey + '/remove-all';
            document.getElementById('removeAllModal').style.display = 'block';
        }

        function showImportModal(sectionKey, sectionName) {
            currentImportSection = sectionKey;
            document.getElementById('importSectionName').textContent = sectionName;

            if (defaultConstants) {
                populateImportItems(sectionKey);
                document.getElementById('importModal').style.display = 'block';
            } else {
                alert('Default constants not loaded. Please refresh the page.');
            }
        }

        function populateImportItems(sectionKey) {
            const itemsList = document.getElementById('importItemsList');
            itemsList.innerHTML = '';

            // Get defaults for this section
            const sectionDefaults = getDefaultsForSection(sectionKey);

            if (sectionDefaults && sectionDefaults.length > 0) {
                sectionDefaults.forEach(item => {
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'import-item';

                    itemDiv.innerHTML = `
                        <input type="checkbox" id="import_${item.key}" value="${item.key}" checked>
                        <div class="import-item-content">
                            ${item.color ? `<span class="color-badge" style="background-color: ${item.color};"></span>` : ''}
                            <span class="import-item-title">${item.title}</span>
                            <code class="import-item-key">${item.key}</code>
                        </div>
                    `;

                    itemsList.appendChild(itemDiv);
                });
            } else {
                itemsList.innerHTML = '<p>No default items available for this section.</p>';
            }
        }

        function getDefaultsForSection(sectionKey) {
            if (!defaultConstants) return [];

            // Map section keys to ApplicationConstants properties
            const sectionMap = {
                'noteTags': 'noteTags',
                'taskTypes': 'taskTypes',
                'memberTags': 'memberTags',
                'carePackageSections': 'carePackageSections',
                'taskCompletionReasons': 'taskCompletionReasons',
                'followUpOutcomes': 'followUpOutcomes',
                'assessmentDueOptions': 'assessmentDueOptions',
                'reviewFrequencies': 'reviewFrequencies',
                'programStatuses': 'programStatuses',
                'programTypes': 'programTypes',
                'attachmentTypes': 'attachmentTypes',
                'uploadKinds': 'uploadTypes',
                'networkStatus': 'networkStatus',
                'planCoverage': 'planCoverage',
                'contactTaskTypes': 'contactTaskTypes',
                'memberTypes': 'memberTypes',
                'memberStatus': 'staffStatus',
                'relationshipTypes': 'relationshipTypes',
                'problemTypes': 'problemTypes',
                'interventionTypes': 'interventionTypes',
                'goalTypes': 'goalTypes',
                'notesSubcategory': 'notesSubcategory',
                'reasons': 'reasons',
                'staffStatus': 'staffStatus'
            };

            const propertyName = sectionMap[sectionKey];
            if (propertyName && defaultConstants[propertyName]) {
                const items = defaultConstants[propertyName];
                // Handle uploadTypes which is an array of strings
                if (propertyName === 'uploadTypes') {
                    return items.map(item => ({ title: item, key: item }));
                }
                return items;
            }

            return [];
        }

        function selectAllDefaults() {
            const checkboxes = document.querySelectorAll('#importItemsList input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = true);
        }

        function deselectAllDefaults() {
            const checkboxes = document.querySelectorAll('#importItemsList input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
        }

        async function performImport() {
            const checkboxes = document.querySelectorAll('#importItemsList input[type="checkbox"]:checked');
            const selectedKeys = Array.from(checkboxes).map(cb => cb.value);

            if (selectedKeys.length === 0) {
                alert('Please select at least one item to import.');
                return;
            }

            const importBtn = document.getElementById('importConfirmBtn');
            importBtn.disabled = true;
            importBtn.textContent = 'Importing...';

            try {
                const response = await fetch(`/org/#(orgID)/constants/${currentImportSection}/import-defaults`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        selectedKeys: selectedKeys
                    })
                });

                if (response.ok) {
                    // Success - refresh the page
                    window.location.reload();
                } else {
                    alert('Failed to import items. Please try again.');
                    importBtn.disabled = false;
                    importBtn.textContent = 'Import Selected';
                }
            } catch (error) {
                console.error('Import error:', error);
                alert('An error occurred. Please try again.');
                importBtn.disabled = false;
                importBtn.textContent = 'Import Selected';
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const deleteModal = document.getElementById('deleteModal');
            const removeAllModal = document.getElementById('removeAllModal');
            const importModal = document.getElementById('importModal');

            if (event.target === deleteModal) {
                deleteModal.style.display = 'none';
            }
            if (event.target === removeAllModal) {
                removeAllModal.style.display = 'none';
            }
            if (event.target === importModal) {
                importModal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
