<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }
        
        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #FD8205, #E97100);
            border-radius: 8px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo-icon::before {
            content: "✚";
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 700;
            color: #1F2937;
        }
        
        .login-title {
            font-size: 24px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            font-size: 16px;
            font-family: 'Graphik', sans-serif;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #FD8205;
            box-shadow: 0 0 0 3px rgba(253, 130, 5, 0.1);
        }
        
        .form-input::placeholder {
            color: #9CA3AF;
        }
        
        .login-button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(to bottom, #FD8205, #E97100);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            font-family: 'Graphik', sans-serif;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(253, 130, 5, 0.3);
        }
        
        .login-button:active {
            transform: translateY(0);
        }
        
        .login-button::before {
            content: "✓";
            font-size: 14px;
        }
        
        .forgot-password {
            margin-top: 20px;
        }
        
        .forgot-password a {
            color: #FD8205;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }
        
        .forgot-password a:hover {
            text-decoration: underline;
        }
        
        .error-message {
            background: #FEF2F2;
            border: 1px solid #FECACA;
            color: #DC2626;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: left;
        }
        
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .loading .login-button::before {
            content: "⟳";
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <div class="logo-icon"></div>
            <div class="logo-text">wellup</div>
        </div>
        
        <h1 class="login-title">Sign In</h1>
        
        #if(error):
        <div class="error-message">
            #(error)
        </div>
        #endif
        
        <form method="POST" action="/admin/login" id="loginForm">
            <div class="form-group">
                <input 
                    type="text" 
                    name="username" 
                    class="form-input" 
                    placeholder="Username"
                    required
                    autocomplete="username"
                    value="#(username)"
                >
            </div>
            
            <div class="form-group">
                <input 
                    type="password" 
                    name="password" 
                    class="form-input" 
                    placeholder="Password"
                    required
                    autocomplete="current-password"
                >
            </div>
            
            <button type="submit" class="login-button">
                Sign in
            </button>
        </form>
        
        <div class="forgot-password">
            <a href="#" onclick="alert('Contact your administrator for password reset')">Forgot Password?</a>
        </div>
    </div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', function() {
            document.body.classList.add('loading');
        });
    </script>
</body>
</html>
