<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>#(program.programName) - #(orgName) - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #E5E7EB;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-info {
            color: #6B7280;
            font-size: 14px;
        }
        
        .logout-btn {
            background: #EF4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .logout-btn:hover {
            background: #DC2626;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: #3B82F6;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .breadcrumb-separator {
            color: #9CA3AF;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 32px;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            color: #6B7280;
            font-size: 16px;
        }
        
        .program-key {
            font-size: 14px;
            color: #6B7280;
            font-family: 'Monaco', 'Menlo', monospace;
            background: #F3F4F6;
            padding: 4px 8px;
            border-radius: 4px;
            margin-top: 8px;
            display: inline-block;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: none;
            border: 1px solid #1470C4;
            color: #1470C4;
        }

        .btn-primary:hover {
            background: #1470C4;
            color: white;
        }

        .btn-secondary {
            background: none;
            border: 1px solid #6B7280;
            color: #6B7280;
        }

        .btn-secondary:hover {
            background: #6B7280;
            color: white;
        }

        .btn-danger {
            background: none;
            border: 1px solid #E97100;
            color: #E97100;
        }

        .btn-danger:hover {
            background: #E97100;
            color: white;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .detail-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #E5E7EB;
        }
        
        .detail-card h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 16px;
        }
        
        .detail-item {
            margin-bottom: 12px;
        }
        
        .detail-label {
            font-size: 12px;
            font-weight: 500;
            color: #6B7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }
        
        .detail-value {
            font-size: 14px;
            color: #1F2937;
        }
        
        .program-type-badge {
            background: #3B82F6;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }
        
        .config-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #E5E7EB;
            margin-bottom: 24px;
        }
        
        .config-section h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 16px;
        }
        
        .assessment-list, .review-list {
            display: grid;
            gap: 12px;
        }
        
        .assessment-item, .review-item {
            background: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
        }
        
        .item-title {
            font-size: 14px;
            font-weight: 500;
            color: #1F2937;
            margin-bottom: 4px;
        }
        
        .item-key {
            font-size: 12px;
            color: #6B7280;
            font-family: 'Monaco', 'Menlo', monospace;
            background: #E5E7EB;
            padding: 2px 4px;
            border-radius: 3px;
            margin-bottom: 8px;
            display: inline-block;
        }
        
        .item-required {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
        }
        
        .required-yes {
            background: #FEE2E2;
            color: #DC2626;
        }
        
        .required-no {
            background: #F3F4F6;
            color: #6B7280;
        }
        
        .json-viewer {
            background: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            color: #374151;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-text">Wellup</div>
            </div>
            
            <div class="user-menu">
                <span class="user-info">Welcome, #(adminUsername)</span>
                <form method="POST" action="/admin/logout" style="display: inline;">
                    <button type="submit" class="logout-btn">Logout</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="breadcrumb">
            <a href="/admin/dashboard">Admin Dashboard</a>
            <span class="breadcrumb-separator">›</span>
            <a href="/admin/org-programs">Organization Program Management</a>
            <span class="breadcrumb-separator">›</span>
            <a href="/admin/org-programs/#(orgID)">#(orgName)</a>
            <span class="breadcrumb-separator">›</span>
            <span>#(program.programName)</span>
        </div>
        
        <div class="page-header">
            <div>
                <h1 class="page-title">#(program.programName)</h1>
                <div class="program-key">#(program.programKey)</div>
                <p class="page-subtitle">#(program.programConfig.description)</p>
            </div>
            <div class="action-buttons">
                <a href="/admin/org-programs/#(orgID)/#(program.programKey)/edit" class="btn btn-primary">Edit Program</a>
                <a href="/admin/org-programs/#(orgID)" class="btn btn-secondary">Back to List</a>
                <form method="POST" action="/admin/org-programs/#(orgID)/#(program.programKey)" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this program? This action cannot be undone.')">Delete</button>
                </form>
            </div>
        </div>
        
        <div class="detail-grid">
            <div class="detail-card">
                <h3>Program Information</h3>
                <div class="detail-item">
                    <div class="detail-label">Program Type</div>
                    <div class="detail-value">
                        <span class="program-type-badge">#(program.programConfig.programType)</span>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Display Name</div>
                    <div class="detail-value">#(program.programConfig.displayName)</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Template ID</div>
                    <div class="detail-value">#(program.programConfig.templateId)</div>
                </div>
            </div>
            
            <div class="detail-card">
                <h3>Timing & Schedule</h3>
                <div class="detail-item">
                    <div class="detail-label">Review Frequency</div>
                    <div class="detail-value">#(program.programConfig.reviewFrequencyDays) days</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Assessment Completion Time</div>
                    <div class="detail-value">#(program.programConfig.daysToCompleteAssessment) days</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Created</div>
                    <div class="detail-value">#(program.createdAt)</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Last Updated</div>
                    <div class="detail-value">#(program.updatedAt)</div>
                </div>
            </div>
        </div>
        
        #if(program.programConfig.requiredAssessments):
        <div class="config-section">
            <h3>Required Assessments (#(count(program.programConfig.requiredAssessments)))</h3>
            <div class="assessment-list">
                #for(assessment in program.programConfig.requiredAssessments):
                <div class="assessment-item">
                    <div class="item-title">#(assessment.title)</div>
                    <div class="item-key">#(assessment.itemKey)</div>
                    <span class="item-required #if(assessment.required):required-yes#else:required-no#endif">
                        #if(assessment.required):Required#else:Optional#endif
                    </span>
                </div>
                #endfor
            </div>
        </div>
        #endif
        
        #if(program.programConfig.programReviews):
        <div class="config-section">
            <h3>Program Reviews (#(count(program.programConfig.programReviews)))</h3>
            <div class="review-list">
                #for(review in program.programConfig.programReviews):
                <div class="review-item">
                    <div class="item-title">#(review.title)</div>
                    <div class="item-key">#(review.itemKey)</div>
                    <span class="item-required #if(review.required):required-yes#else:required-no#endif">
                        #if(review.required):Required#else:Optional#endif
                    </span>
                </div>
                #endfor
            </div>
        </div>
        #endif
        
        <div class="config-section">
            <h3>Full Configuration (JSON)</h3>
            <div class="json-viewer">#(program.programConfig)</div>
        </div>
    </div>
</body>
</html>
