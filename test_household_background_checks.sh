#!/bin/bash

# Test script for Household Background Check System
# Tests both member-level and household-level background checks

set -e

# Configuration
BASE_URL="http://localhost:8080"
ADMIN_TOKEN=""
MEMBER_ID=""
HOUSEHOLD_ID=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if server is running
check_server() {
    log_info "Checking if server is running..."
    if curl -s "$BASE_URL" > /dev/null; then
        log_success "Server is running"
    else
        log_error "Server is not running. Please start with: swift run App"
        exit 1
    fi
}

# Get admin token
get_admin_token() {
    log_info "Getting admin token..."
    echo "Please provide your admin token:"
    read -r ADMIN_TOKEN
    
    if [ -z "$ADMIN_TOKEN" ]; then
        log_error "Admin token is required"
        exit 1
    fi
    
    log_success "Admin token set"
}

# Get test IDs
get_test_ids() {
    log_info "Getting test member and household IDs..."
    echo "Please provide a test member ID:"
    read -r MEMBER_ID
    
    echo "Please provide a test household ID:"
    read -r HOUSEHOLD_ID
    
    if [ -z "$MEMBER_ID" ] || [ -z "$HOUSEHOLD_ID" ]; then
        log_error "Both member ID and household ID are required"
        exit 1
    fi
    
    log_success "Test IDs set - Member: $MEMBER_ID, Household: $HOUSEHOLD_ID"
}

# Test member background check
test_member_background_check() {
    log_info "Testing member background check..."
    
    local response
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -d '{
            "fullName": "LOPERA, HECTOR",
            "zipCode": "33018"
        }' \
        "$BASE_URL/api/background-checks/members/$MEMBER_ID/trigger")
    
    local http_code
    http_code=$(echo "$response" | tail -n1)
    local body
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 201 ]; then
        log_success "Member background check triggered successfully"
        echo "Response: $body"
    else
        log_error "Member background check failed with HTTP $http_code"
        echo "Response: $body"
    fi
}

# Test household background check
test_household_background_check() {
    log_info "Testing household background check..."
    
    local response
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -d '{
            "zipCode": "33018"
        }' \
        "$BASE_URL/api/background-checks/households/$HOUSEHOLD_ID/trigger")
    
    local http_code
    http_code=$(echo "$response" | tail -n1)
    local body
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 201 ]; then
        log_success "Household background check triggered successfully"
        echo "Response: $body"
    else
        log_error "Household background check failed with HTTP $http_code"
        echo "Response: $body"
    fi
}

# Test review queue
test_review_queue() {
    log_info "Testing background check review queue..."
    
    local response
    response=$(curl -s -w "\n%{http_code}" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        "$BASE_URL/api/background-checks/queue")
    
    local http_code
    http_code=$(echo "$response" | tail -n1)
    local body
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ]; then
        log_success "Review queue retrieved successfully"
        echo "Queue items found: $(echo "$body" | jq '.data | length' 2>/dev/null || echo "Unable to parse JSON")"
    else
        log_error "Review queue retrieval failed with HTTP $http_code"
        echo "Response: $body"
    fi
}

# Main execution
main() {
    echo "🔍 Household Background Check System Test"
    echo "========================================"
    
    check_server
    get_admin_token
    get_test_ids
    
    echo ""
    echo "🧪 Running Tests..."
    echo "==================="
    
    test_member_background_check
    echo ""
    
    test_household_background_check
    echo ""
    
    test_review_queue
    echo ""
    
    log_success "All tests completed!"
    echo ""
    echo "📋 Next Steps:"
    echo "- Check the review queue in the admin dashboard"
    echo "- Verify that both member and household checks appear"
    echo "- Test the approval/rejection workflow"
}

# Run main function
main "$@"
