# Admin Login System for Organization Dropdown Manager

This document describes the new admin login system that protects the Organization Dropdown Manager.

## Overview

The admin login system provides secure access to the Organization Dropdown Manager dashboard. It uses session-based authentication with a clean, Wellup-branded login interface.

## Features

- **Secure Login**: Session-based authentication with 24-hour timeout
- **Wellup Design**: Login page matches the provided Wellup design mockup
- **Organization Selection**: After login, admins can select which organization to manage
- **Protected Routes**: All org dropdown routes require admin authentication
- **Auto-redirect**: Logged-in users are automatically redirected to the dashboard

## URLs

- **Admin Login**: `/admin/login`
- **Admin Dashboard**: `/admin/dashboard` (shows organization selection)
- **Organization Management**: `/org/{orgID}` (requires admin login)

## Default Credentials

The system includes these default admin credentials:

- Username: `admin`, Password: `admin123`
- Username: `wellup_admin`, Password: `wellup2024!`
- Username: `kyle`, Password: `kyle123`

> **Security Note**: In production, replace these hardcoded credentials with a proper database-backed user system with hashed passwords.

## How It Works

### 1. Login Flow
1. User visits `/admin/login`
2. Enters username and password
3. System validates credentials
4. On success, creates admin session and redirects to `/admin/dashboard`
5. On failure, shows error message

### 2. Dashboard Flow
1. Admin dashboard shows all available organizations
2. Admin clicks "Manage Dropdowns" for desired organization
3. Redirects to `/org/{orgID}` (the existing dropdown manager)

### 3. Session Management
- Sessions last 24 hours
- Automatic logout on session timeout
- Manual logout via logout button
- Session data includes admin username and login time

## Technical Implementation

### Files Created/Modified

**New Files:**
- `Sources/App/Controllers/AdminAuthController.swift` - Handles login/logout/dashboard
- `Resources/Views/admin-login.leaf` - Login page with Wellup design
- `Resources/Views/admin-dashboard.leaf` - Organization selection dashboard

**Modified Files:**
- `Sources/App/Controllers/OrgDropdownController.swift` - Added admin auth middleware
- `Sources/App/routes.swift` - Registered AdminAuthController
- `Sources/App/Controllers/Tasks/TasksController.swift` - Updated admin check

### Key Components

1. **AdminAuthController**: Manages login, logout, and dashboard
2. **AdminAuthMiddleware**: Protects routes requiring admin access
3. **Session Management**: Uses Vapor's built-in session system
4. **Organization Integration**: Fetches orgs from database for dashboard

## Usage Examples

### Accessing the System
1. Start your Vapor server
2. Navigate to `http://localhost:8080/admin/login`
3. Login with admin credentials
4. Select an organization to manage
5. Use the existing dropdown management interface

### Customizing Credentials
To add/modify admin credentials, edit the `validCredentials` dictionary in `AdminAuthController.swift`:

```swift
let validCredentials = [
    "your_username": "your_password",
    "another_admin": "secure_password"
]
```

### Session Timeout
To modify the session timeout, change the `sessionTimeout` value in `AdminAuthMiddleware`:

```swift
let sessionTimeout: TimeInterval = 24 * 60 * 60 // 24 hours
```

## Security Considerations

1. **Hardcoded Passwords**: Replace with database-backed authentication
2. **Password Hashing**: Implement proper password hashing (bcrypt, etc.)
3. **HTTPS**: Use HTTPS in production
4. **Session Security**: Consider additional session security measures
5. **Rate Limiting**: Add rate limiting for login attempts

## Future Enhancements

- Database-backed user management
- Password reset functionality
- Role-based permissions
- Audit logging
- Multi-factor authentication
- Remember me functionality

## Troubleshooting

### Common Issues

1. **Build Errors**: Ensure all files are properly imported and syntax is correct
2. **Session Issues**: Check that sessions are enabled in your Vapor configuration
3. **Route Conflicts**: Verify no conflicting routes exist
4. **Database Connection**: Ensure database is connected for organization fetching

### Debug Tips

- Check server logs for authentication errors
- Verify session data in browser developer tools
- Test with different browsers to isolate session issues
- Use Vapor's built-in logging for debugging

## Support

For issues or questions about the admin login system, check:
1. Server logs for error messages
2. Browser developer console for client-side issues
3. Database connectivity for organization loading issues
