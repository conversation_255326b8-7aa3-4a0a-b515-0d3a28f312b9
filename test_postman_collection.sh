#!/bin/bash

# 🚀 Background Check System - Postman Collection Test Runner
# This script runs the Postman collection using Newman CLI

set -e

echo "🔥 BACKGROUND CHECK SYSTEM - POSTMAN TESTING"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if <PERSON> is installed
if ! command -v newman &> /dev/null; then
    echo -e "${RED}❌ Newman CLI not found!${NC}"
    echo -e "${YELLOW}Install with: npm install -g newman${NC}"
    exit 1
fi

# Configuration
COLLECTION_FILE="Background_Check_System.postman_collection.json"
ENVIRONMENT_FILE="Background_Check_Environment.postman_environment.json"
BASE_URL="${BASE_URL:-http://localhost:8080}"
ADMIN_TOKEN="${ADMIN_TOKEN:-}"
MEMBER_ID="${MEMBER_ID:-}"

echo -e "${BLUE}📋 Configuration:${NC}"
echo "  Collection: $COLLECTION_FILE"
echo "  Environment: $ENVIRONMENT_FILE"
echo "  Base URL: $BASE_URL"
echo "  Admin Token: ${ADMIN_TOKEN:0:10}..."
echo "  Member ID: $MEMBER_ID"
echo ""

# Check if files exist
if [[ ! -f "$COLLECTION_FILE" ]]; then
    echo -e "${RED}❌ Collection file not found: $COLLECTION_FILE${NC}"
    exit 1
fi

if [[ ! -f "$ENVIRONMENT_FILE" ]]; then
    echo -e "${RED}❌ Environment file not found: $ENVIRONMENT_FILE${NC}"
    exit 1
fi

# Validate required environment variables
if [[ -z "$ADMIN_TOKEN" ]]; then
    echo -e "${YELLOW}⚠️  ADMIN_TOKEN not set. Set it with:${NC}"
    echo "  export ADMIN_TOKEN='your_admin_token_here'"
    echo ""
fi

if [[ -z "$MEMBER_ID" ]]; then
    echo -e "${YELLOW}⚠️  MEMBER_ID not set. Set it with:${NC}"
    echo "  export MEMBER_ID='your_member_uuid_here'"
    echo ""
fi

# Function to run specific folder
run_folder() {
    local folder_name="$1"
    local description="$2"
    
    echo -e "${BLUE}🧪 Testing: $description${NC}"
    
    newman run "$COLLECTION_FILE" \
        --environment "$ENVIRONMENT_FILE" \
        --folder "$folder_name" \
        --env-var "baseUrl=$BASE_URL" \
        --env-var "adminToken=$ADMIN_TOKEN" \
        --env-var "memberId=$MEMBER_ID" \
        --reporters cli,json \
        --reporter-json-export "test-results-$(echo $folder_name | tr ' ' '-' | tr '[:upper:]' '[:lower:]').json" \
        --timeout-request 30000 \
        --delay-request 1000
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✅ $description - PASSED${NC}"
    else
        echo -e "${RED}❌ $description - FAILED${NC}"
        return 1
    fi
    echo ""
}

# Function to run full collection
run_full_collection() {
    echo -e "${BLUE}🚀 Running Full Collection${NC}"
    
    newman run "$COLLECTION_FILE" \
        --environment "$ENVIRONMENT_FILE" \
        --env-var "baseUrl=$BASE_URL" \
        --env-var "adminToken=$ADMIN_TOKEN" \
        --env-var "memberId=$MEMBER_ID" \
        --reporters cli,htmlextra,json \
        --reporter-htmlextra-export "background-check-test-report.html" \
        --reporter-json-export "background-check-test-results.json" \
        --timeout-request 30000 \
        --delay-request 2000 \
        --bail
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED!${NC}"
        echo -e "${BLUE}📊 Report generated: background-check-test-report.html${NC}"
    else
        echo -e "${RED}💥 SOME TESTS FAILED!${NC}"
        return 1
    fi
}

# Main execution
case "${1:-full}" in
    "background-check")
        run_folder "🔍 Background Check Operations" "Background Check Operations"
        ;;
    "review")
        run_folder "✅ Review & Approval" "Review & Approval Workflow"
        ;;
    "ui")
        run_folder "🖥️ Admin UI Routes" "Admin UI Routes"
        ;;
    "full")
        echo -e "${YELLOW}🔥 Running complete test suite...${NC}"
        echo ""
        run_full_collection
        ;;
    "help")
        echo "Usage: $0 [test-type]"
        echo ""
        echo "Test Types:"
        echo "  background-check  - Test background check operations"
        echo "  review           - Test review and approval workflow"
        echo "  ui               - Test admin UI routes"
        echo "  full             - Run complete test suite (default)"
        echo "  help             - Show this help"
        echo ""
        echo "Environment Variables:"
        echo "  BASE_URL         - API base URL (default: http://localhost:8080)"
        echo "  ADMIN_TOKEN      - Admin bearer token (required)"
        echo "  MEMBER_ID        - Test member UUID (required)"
        echo ""
        echo "Example:"
        echo "  export ADMIN_TOKEN='eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'"
        echo "  export MEMBER_ID='550e8400-e29b-41d4-a716-************'"
        echo "  $0 full"
        ;;
    *)
        echo -e "${RED}❌ Unknown test type: $1${NC}"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac

echo -e "${GREEN}🏁 Testing completed!${NC}"
